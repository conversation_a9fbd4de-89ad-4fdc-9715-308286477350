#!/usr/bin/env python3
"""
Simple Setup Monitor for Shyam Trading RAG System
=================================================

This script provides a simple way to monitor the setup progress
without relying on complex terminal interactions.
"""

import os
import time
from pathlib import Path
from datetime import datetime

def get_log_status():
    """Get the current status from the log file"""
    log_file = "shyam_rag_setup.log"
    
    if not Path(log_file).exists():
        return "LOG_NOT_FOUND", []
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            return "LOG_EMPTY", []
        
        # Get last few lines
        recent_lines = [line.strip() for line in lines[-10:] if line.strip()]
        
        # Check for completion or errors
        full_content = ''.join(lines)
        
        if "SETUP COMPLETED SUCCESSFULLY" in full_content:
            return "COMPLETED", recent_lines
        elif "Setup failed" in full_content or "ERROR" in full_content:
            return "FAILED", recent_lines
        elif "Installing package group" in full_content:
            return "INSTALLING", recent_lines
        else:
            return "UNKNOWN", recent_lines
            
    except Exception as e:
        return f"ERROR: {e}", []

def check_processes():
    """Check if setup process is still running"""
    try:
        import psutil
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] == 'python.exe':
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'setup_rag_system.py' in cmdline:
                        return True, proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False, None
    except ImportError:
        # psutil not available, can't check processes
        return None, None

def main():
    print("🔍 Shyam Trading RAG Setup Monitor")
    print("=" * 50)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check log status
    status, recent_lines = get_log_status()
    
    print(f"📊 Setup Status: {status}")
    print()
    
    if status == "LOG_NOT_FOUND":
        print("❌ Setup log file not found.")
        print("   The setup process may not have started yet.")
        print("   Run: python setup_rag_system.py --use-existing-env")
        
    elif status == "LOG_EMPTY":
        print("📝 Setup log is empty.")
        print("   The setup process may be starting...")
        
    elif status == "COMPLETED":
        print("🎉 SETUP COMPLETED SUCCESSFULLY!")
        print("   You can now proceed to test the RAG system.")
        
    elif status == "FAILED":
        print("❌ Setup failed!")
        print("   Check the log file for error details.")
        
    elif status == "INSTALLING":
        print("⏳ Setup is in progress...")
        print("   Please be patient, especially during PyTorch installation.")
        
    else:
        print(f"❓ Unknown status: {status}")
    
    # Show recent log lines
    if recent_lines:
        print("\n📝 Recent Log Entries:")
        for line in recent_lines:
            print(f"   {line}")
    
    # Check if process is running
    is_running, pid = check_processes()
    if is_running is not None:
        if is_running:
            print(f"\n🔄 Setup process is running (PID: {pid})")
        else:
            print(f"\n⏹️  No setup process detected")
    
    # File size info
    log_file = Path("shyam_rag_setup.log")
    if log_file.exists():
        size = log_file.stat().st_size
        modified = datetime.fromtimestamp(log_file.stat().st_mtime)
        print(f"\n📁 Log file: {size} bytes, last modified: {modified.strftime('%H:%M:%S')}")
    
    print("\n💡 Tips:")
    print("   - Run this script periodically to check progress")
    print("   - PyTorch installation can take 15-30 minutes")
    print("   - Check shyam_rag_setup.log for detailed information")

if __name__ == "__main__":
    main()
