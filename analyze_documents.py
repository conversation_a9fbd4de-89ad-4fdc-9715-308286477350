"""
Document Analysis Script for Shyam Trading Company
=================================================

This script analyzes the actual document structure to understand
how names and other fields are positioned in your PDFs and DOCX files.
"""

import os
import pdfplumber
from docx import Document
import re

def analyze_pdf(file_path):
    """Analyze PDF structure"""
    print(f"\n📄 Analyzing PDF: {file_path}")
    try:
        with pdfplumber.open(file_path) as pdf:
            if len(pdf.pages) > 0:
                page = pdf.pages[0]
                text = page.extract_text()
                if text:
                    print("=" * 60)
                    print("FULL TEXT:")
                    print("=" * 60)
                    print(text)
                    print("\n" + "=" * 60)
                    print("LINE BY LINE:")
                    print("=" * 60)
                    lines = text.split('\n')
                    for i, line in enumerate(lines, 1):
                        if line.strip():
                            print(f"{i:2d}: '{line.strip()}'")
                    
                    # Look for potential customer names
                    print("\n" + "=" * 60)
                    print("POTENTIAL CUSTOMER NAMES:")
                    print("=" * 60)
                    name_patterns = [
                        r'Mr\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*',
                        r'Mrs\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*', 
                        r'Ms\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*',
                        r'Dr\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*',
                        r'[A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s+(?:Pvt\.?\s*Ltd\.?|Ltd\.?|Company|Co\.?))?'
                    ]
                    
                    for pattern in name_patterns:
                        matches = re.findall(pattern, text)
                        if matches:
                            print(f"Pattern '{pattern}': {matches}")
                    
                    return text
                else:
                    print("No text extracted from PDF")
            else:
                print("PDF has no pages")
    except Exception as e:
        print(f"Error analyzing PDF: {str(e)}")
        import traceback
        traceback.print_exc()
    return None

def analyze_docx(file_path):
    """Analyze DOCX structure"""
    print(f"\n📄 Analyzing DOCX: {file_path}")
    try:
        doc = Document(file_path)
        paragraphs = [p.text.strip() for p in doc.paragraphs if p.text.strip()]
        
        if paragraphs:
            text = '\n'.join(paragraphs)
            print("=" * 60)
            print("FULL TEXT:")
            print("=" * 60)
            print(text)
            print("\n" + "=" * 60)
            print("PARAGRAPH BY PARAGRAPH:")
            print("=" * 60)
            for i, para in enumerate(paragraphs, 1):
                print(f"{i:2d}: '{para}'")
            
            # Look for potential customer names
            print("\n" + "=" * 60)
            print("POTENTIAL CUSTOMER NAMES:")
            print("=" * 60)
            name_patterns = [
                r'Mr\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*',
                r'Mrs\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*', 
                r'Ms\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*',
                r'Dr\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*',
                r'[A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s+(?:Pvt\.?\s*Ltd\.?|Ltd\.?|Company|Co\.?))?'
            ]
            
            for pattern in name_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    print(f"Pattern '{pattern}': {matches}")
            
            return text
        else:
            print("No text extracted from DOCX")
    except Exception as e:
        print(f"Error analyzing DOCX: {str(e)}")
        import traceback
        traceback.print_exc()
    return None

def main():
    """Analyze sample documents"""
    print("🔍 Analyzing Shyam Trading Company Documents")
    print("=" * 80)
    
    # Sample files to analyze
    sample_files = [
        "Papa/Invoice 25-26/Invoice 1 - 4-5.pdf",
        "Papa/Mr. Aditya.pdf", 
        "Papa/Account Statement (DP Jain) .pdf",
        "Papa/Mr. Aditya.docx",
        "Papa/Account Statement (DP Jain) .docx"
    ]
    
    for file_path in sample_files:
        if os.path.exists(file_path):
            if file_path.endswith('.pdf'):
                analyze_pdf(file_path)
            elif file_path.endswith('.docx'):
                analyze_docx(file_path)
            print("\n" + "=" * 80)
        else:
            print(f"❌ File not found: {file_path}")

if __name__ == "__main__":
    main()
