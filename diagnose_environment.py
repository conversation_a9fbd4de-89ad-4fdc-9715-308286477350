#!/usr/bin/env python3
"""
Environment Diagnostic Script for Shyam Trading RAG System
==========================================================

This script diagnoses the current Python environment and checks
for potential issues that might be causing terminal hangs.
"""

import sys
import os
import platform
import subprocess
from pathlib import Path

def print_separator(title):
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def check_basic_info():
    print_separator("BASIC SYSTEM INFORMATION")
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.architecture()}")
    print(f"Python Version: {sys.version}")
    print(f"Python Executable: {sys.executable}")
    print(f"Current Working Directory: {os.getcwd()}")
    print(f"PATH: {os.environ.get('PATH', 'Not found')[:200]}...")

def check_virtual_environments():
    print_separator("VIRTUAL ENVIRONMENT CHECK")
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✓ Currently in a virtual environment")
        print(f"  Base prefix: {getattr(sys, 'base_prefix', 'Not available')}")
        print(f"  Prefix: {sys.prefix}")
    else:
        print("⚠ Not in a virtual environment")
    
    # Check for existing virtual environments
    venv_paths = [
        "shyam_rag_env",
        "shyam_env",
        "venv",
        ".venv"
    ]
    
    print("\nExisting virtual environments:")
    for venv_path in venv_paths:
        if Path(venv_path).exists():
            print(f"  ✓ Found: {venv_path}")
            # Check if it has Python
            if platform.system() == "Windows":
                python_path = Path(venv_path) / "Scripts" / "python.exe"
            else:
                python_path = Path(venv_path) / "bin" / "python"
            
            if python_path.exists():
                print(f"    Python executable: {python_path}")
            else:
                print(f"    ⚠ No Python executable found")
        else:
            print(f"  - Not found: {venv_path}")

def check_installed_packages():
    print_separator("INSTALLED PACKAGES CHECK")
    
    important_packages = [
        "torch", "transformers", "sentence-transformers",
        "numpy", "pandas", "faiss-cpu", "spacy",
        "PyPDF2", "fitz", "docx", "mammoth",
        "langchain", "fastapi", "uvicorn"
    ]
    
    print("Checking important packages:")
    for package in important_packages:
        try:
            __import__(package)
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ✗ {package} - Not installed")
        except Exception as e:
            print(f"  ⚠ {package} - Error: {e}")

def check_file_structure():
    print_separator("FILE STRUCTURE CHECK")
    
    important_files = [
        "shyam_rag_system.py",
        "shyam_rag_api.py", 
        "setup_rag_system.py",
        "requirements_rag.txt",
        "test_rag_system.py"
    ]
    
    print("RAG system files:")
    for file_name in important_files:
        if Path(file_name).exists():
            size = Path(file_name).stat().st_size
            print(f"  ✓ {file_name} ({size} bytes)")
        else:
            print(f"  ✗ {file_name} - Missing")
    
    # Check Papa directory
    papa_dir = Path("Papa")
    if papa_dir.exists():
        pdf_files = list(papa_dir.glob("**/*.pdf"))
        docx_files = list(papa_dir.glob("**/*.docx"))
        print(f"\nPapa directory:")
        print(f"  ✓ Directory exists")
        print(f"  PDF files: {len(pdf_files)}")
        print(f"  DOCX files: {len(docx_files)}")
        print(f"  Total documents: {len(pdf_files) + len(docx_files)}")
    else:
        print(f"\n  ✗ Papa directory not found")

def check_permissions():
    print_separator("PERMISSIONS CHECK")
    
    # Check write permissions
    test_file = "permission_test.tmp"
    try:
        with open(test_file, "w") as f:
            f.write("test")
        os.remove(test_file)
        print("✓ Write permissions in current directory")
    except Exception as e:
        print(f"✗ Write permission issue: {e}")
    
    # Check if we can create directories
    test_dir = "test_dir_tmp"
    try:
        os.makedirs(test_dir, exist_ok=True)
        os.rmdir(test_dir)
        print("✓ Directory creation permissions")
    except Exception as e:
        print(f"✗ Directory creation issue: {e}")

def check_subprocess():
    print_separator("SUBPROCESS CHECK")
    
    # Test basic subprocess functionality
    try:
        result = subprocess.run([sys.executable, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ Basic subprocess functionality works")
            print(f"  Output: {result.stdout.strip()}")
        else:
            print(f"✗ Subprocess returned error code: {result.returncode}")
            print(f"  Error: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("✗ Subprocess timed out")
    except Exception as e:
        print(f"✗ Subprocess error: {e}")

def main():
    print("🔍 Shyam Trading RAG System - Environment Diagnostics")
    print("=" * 60)
    
    try:
        check_basic_info()
        check_virtual_environments()
        check_installed_packages()
        check_file_structure()
        check_permissions()
        check_subprocess()
        
        print_separator("DIAGNOSTIC COMPLETE")
        print("✅ Environment diagnostic completed successfully!")
        print("\nIf you see any ✗ or ⚠ symbols above, those indicate potential issues")
        print("that might need to be addressed before running the RAG system.")
        
    except Exception as e:
        print(f"\n❌ Diagnostic failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
