"""
Shyam Trading Company - RAG API Interface
=========================================

FastAPI-based REST API for the RAG system, designed for n8n integration
and other external applications.

Endpoints:
- POST /query - Query the RAG system
- GET /stats - Get database statistics
- POST /rebuild - Rebuild the database
- GET /health - Health check

Usage:
    python shyam_rag_api.py
    
Then access: http://localhost:8000/docs for API documentation

Author: AI Assistant for Shyam Trading Company
Date: 2025
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import uvicorn
import logging
from datetime import datetime
import os

# Import our RAG system
from shyam_rag_system import ShyamRAGSystem, create_rag_system

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Shyam Trading Company RAG API",
    description="Advanced RAG system for business document retrieval",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for n8n integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global RAG system instance
rag_system: Optional[ShyamRAGSystem] = None

# Pydantic models for API
class QueryRequest(BaseModel):
    question: str = Field(..., description="The question to search for")
    document_type: Optional[str] = Field(None, description="Filter by document type (invoice, quotation, account_statement)")
    client_name: Optional[str] = Field(None, description="Filter by client name")
    top_k: int = Field(5, description="Number of results to return", ge=1, le=20)

class QueryResponse(BaseModel):
    success: bool
    query: str
    total_results: int
    results: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class StatsResponse(BaseModel):
    success: bool
    total_documents: int
    document_types: Dict[str, int]
    unique_clients: int
    clients: List[str]
    file_types: Dict[str, int]

class RebuildRequest(BaseModel):
    source_directory: Optional[str] = Field("Papa/", description="Source directory for documents")
    force_rebuild: bool = Field(False, description="Force rebuild even if database exists")

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    database_ready: bool
    total_documents: int

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize the RAG system on startup"""
    global rag_system
    
    try:
        logger.info("Initializing RAG system...")
        rag_system = create_rag_system()
        
        # Try to load existing database
        if os.path.exists("shyam_rag_db"):
            rag_system.build_database(force_rebuild=False)
            logger.info("RAG system initialized with existing database")
        else:
            logger.info("No existing database found. Use /rebuild endpoint to create one.")
            
    except Exception as e:
        logger.error(f"Failed to initialize RAG system: {e}")
        rag_system = None

# API Endpoints
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    database_ready = rag_system is not None and rag_system.is_built
    total_docs = 0
    
    if database_ready:
        try:
            stats = rag_system.get_document_stats()
            total_docs = stats.get('total_documents', 0)
        except:
            database_ready = False
    
    return HealthResponse(
        status="healthy" if rag_system is not None else "unhealthy",
        timestamp=datetime.now().isoformat(),
        database_ready=database_ready,
        total_documents=total_docs
    )

@app.post("/query", response_model=QueryResponse)
async def query_documents(request: QueryRequest):
    """Query the RAG system for relevant documents"""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    if not rag_system.is_built:
        raise HTTPException(status_code=503, detail="Database not built. Use /rebuild endpoint first.")
    
    try:
        result = rag_system.query(
            question=request.question,
            document_type=request.document_type,
            client_name=request.client_name,
            top_k=request.top_k
        )
        
        return QueryResponse(
            success=True,
            query=result['query'],
            total_results=result['metadata']['total_results'],
            results=result['results'],
            metadata=result['metadata']
        )
        
    except Exception as e:
        logger.error(f"Query failed: {e}")
        raise HTTPException(status_code=500, detail=f"Query failed: {str(e)}")

@app.get("/stats", response_model=StatsResponse)
async def get_database_stats():
    """Get database statistics"""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    if not rag_system.is_built:
        raise HTTPException(status_code=503, detail="Database not built")
    
    try:
        stats = rag_system.get_document_stats()
        
        return StatsResponse(
            success=True,
            total_documents=stats['total_documents'],
            document_types=stats['document_types'],
            unique_clients=stats['unique_clients'],
            clients=stats['clients'],
            file_types=stats['file_types']
        )
        
    except Exception as e:
        logger.error(f"Stats retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=f"Stats retrieval failed: {str(e)}")

@app.post("/rebuild")
async def rebuild_database(request: RebuildRequest, background_tasks: BackgroundTasks):
    """Rebuild the document database"""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    def rebuild_task():
        try:
            logger.info(f"Starting database rebuild from {request.source_directory}")
            rag_system.build_database(
                source_directory=request.source_directory,
                force_rebuild=request.force_rebuild
            )
            logger.info("Database rebuild completed")
        except Exception as e:
            logger.error(f"Database rebuild failed: {e}")
    
    background_tasks.add_task(rebuild_task)
    
    return {
        "success": True,
        "message": "Database rebuild started in background",
        "source_directory": request.source_directory,
        "force_rebuild": request.force_rebuild
    }

@app.get("/clients")
async def get_clients():
    """Get list of all clients in the database"""
    if not rag_system or not rag_system.is_built:
        raise HTTPException(status_code=503, detail="Database not ready")
    
    try:
        stats = rag_system.get_document_stats()
        return {
            "success": True,
            "clients": stats['clients'],
            "total_clients": stats['unique_clients']
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/document-types")
async def get_document_types():
    """Get list of document types in the database"""
    if not rag_system or not rag_system.is_built:
        raise HTTPException(status_code=503, detail="Database not ready")
    
    try:
        stats = rag_system.get_document_stats()
        return {
            "success": True,
            "document_types": list(stats['document_types'].keys()),
            "distribution": stats['document_types']
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search/client/{client_name}")
async def search_by_client(client_name: str, top_k: int = 10):
    """Search documents by specific client"""
    if not rag_system or not rag_system.is_built:
        raise HTTPException(status_code=503, detail="Database not ready")
    
    try:
        result = rag_system.search_by_client(client_name, top_k)
        return {
            "success": True,
            "client_name": client_name,
            "results": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search/type/{doc_type}")
async def search_by_document_type(doc_type: str, top_k: int = 10):
    """Search documents by type"""
    if not rag_system or not rag_system.is_built:
        raise HTTPException(status_code=503, detail="Database not ready")
    
    valid_types = ["invoice", "quotation", "account_statement", "other"]
    if doc_type not in valid_types:
        raise HTTPException(status_code=400, detail=f"Invalid document type. Must be one of: {valid_types}")
    
    try:
        result = rag_system.search_by_document_type(doc_type, top_k)
        return {
            "success": True,
            "document_type": doc_type,
            "results": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/export/n8n")
async def export_for_n8n(request: QueryRequest):
    """Export query results in n8n-friendly format"""
    if not rag_system or not rag_system.is_built:
        raise HTTPException(status_code=503, detail="Database not ready")
    
    try:
        # Get query results
        result = rag_system.query(
            question=request.question,
            document_type=request.document_type,
            client_name=request.client_name,
            top_k=request.top_k
        )
        
        # Export in n8n format
        n8n_result = rag_system.export_for_n8n(result)
        return n8n_result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Run the API server
if __name__ == "__main__":
    uvicorn.run(
        "shyam_rag_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
