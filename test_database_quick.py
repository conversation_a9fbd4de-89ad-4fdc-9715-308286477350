#!/usr/bin/env python3
"""
Quick Database Test for Shyam Trading RAG System
===============================================

This script quickly tests if the database was created successfully
and provides basic functionality verification.
"""

import sys
import os
from pathlib import Path

def test_database_files():
    """Test if database files exist"""
    print("🔍 Checking Database Files...")
    
    db_path = Path("shyam_rag_db")
    
    if not db_path.exists():
        print("❌ Database directory not found")
        return False
    
    required_files = [
        "faiss_index.bin",
        "documents.pkl", 
        "embeddings.npy",
        "bm25.pkl"
    ]
    
    for file_name in required_files:
        file_path = db_path / file_name
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"✅ {file_name}: {size_mb:.1f} MB")
        else:
            print(f"❌ {file_name}: Missing")
            return False
    
    return True

def test_basic_import():
    """Test basic RAG system import"""
    print("\n🔍 Testing RAG System Import...")
    
    try:
        from shyam_rag_system import ShyamRAGSystem
        print("✅ RAG system import successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_database_loading():
    """Test database loading"""
    print("\n🔍 Testing Database Loading...")
    
    try:
        from shyam_rag_system import ShyamRAGSystem
        
        rag = ShyamRAGSystem()
        print("✅ RAG system initialized")
        
        # Try to load existing database
        rag.build_database("Papa/", force_rebuild=False)
        print("✅ Database loaded")
        
        if rag.is_built:
            print("✅ Database is ready for queries")
            return True, rag
        else:
            print("❌ Database not marked as built")
            return False, None
            
    except Exception as e:
        print(f"❌ Database loading failed: {e}")
        return False, None

def test_simple_query(rag):
    """Test a simple query"""
    print("\n🔍 Testing Simple Query...")
    
    try:
        result = rag.query("construction", top_k=3)
        
        print(f"✅ Query successful")
        print(f"   Results found: {len(result['results'])}")
        
        if result['results']:
            top_result = result['results'][0]
            print(f"   Top result score: {top_result['score']:.3f}")
            print(f"   Source: {top_result['metadata']['file_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Query failed: {e}")
        return False

def main():
    print("🚀 Shyam Trading RAG System - Quick Database Test")
    print("=" * 60)
    
    # Test 1: Database files
    files_ok = test_database_files()
    
    # Test 2: Import
    import_ok = test_basic_import()
    
    # Test 3: Database loading
    if files_ok and import_ok:
        db_ok, rag = test_database_loading()
        
        # Test 4: Simple query
        if db_ok and rag:
            query_ok = test_simple_query(rag)
            
            if query_ok:
                print("\n🎉 ALL TESTS PASSED!")
                print("✅ Database is working correctly")
                print("✅ Ready for production use")
                return True
    
    print("\n❌ Some tests failed")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
