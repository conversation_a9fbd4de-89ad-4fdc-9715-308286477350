"""
Debug script to check package availability and basic functionality
"""

import sys
import os

print("🔍 Python Package Debug Script")
print("=" * 50)
print(f"Python version: {sys.version}")
print(f"Current directory: {os.getcwd()}")
print(f"Python path: {sys.executable}")

# Test pdfplumber
print("\n📄 Testing pdfplumber...")
try:
    import pdfplumber
    print("✅ pdfplumber imported successfully")
    print(f"   Version: {pdfplumber.__version__ if hasattr(pdfplumber, '__version__') else 'Unknown'}")
    
    # Test with a sample PDF
    pdf_files = []
    for root, dirs, files in os.walk("Papa"):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
                break
        if pdf_files:
            break
    
    if pdf_files:
        test_pdf = pdf_files[0]
        print(f"   Testing with: {test_pdf}")
        try:
            with pdfplumber.open(test_pdf) as pdf:
                print(f"   ✅ PDF opened successfully - {len(pdf.pages)} pages")
                if pdf.pages:
                    text = pdf.pages[0].extract_text()
                    if text:
                        print(f"   ✅ Text extracted - {len(text)} characters")
                        print(f"   First 100 chars: {text[:100]}...")
                    else:
                        print("   ⚠️  No text extracted from first page")
        except Exception as e:
            print(f"   ❌ Error testing PDF: {str(e)}")
    else:
        print("   ⚠️  No PDF files found for testing")
        
except ImportError as e:
    print(f"❌ pdfplumber not available: {str(e)}")

# Test python-docx
print("\n📄 Testing python-docx...")
try:
    from docx import Document
    print("✅ python-docx imported successfully")
    
    # Test with a sample DOCX
    docx_files = []
    for root, dirs, files in os.walk("Papa"):
        for file in files:
            if file.lower().endswith('.docx'):
                docx_files.append(os.path.join(root, file))
                break
        if docx_files:
            break
    
    if docx_files:
        test_docx = docx_files[0]
        print(f"   Testing with: {test_docx}")
        try:
            doc = Document(test_docx)
            print(f"   ✅ DOCX opened successfully - {len(doc.paragraphs)} paragraphs")
            text = "\n".join([p.text for p in doc.paragraphs if p.text.strip()])
            if text:
                print(f"   ✅ Text extracted - {len(text)} characters")
                print(f"   First 100 chars: {text[:100]}...")
            else:
                print("   ⚠️  No text extracted")
        except Exception as e:
            print(f"   ❌ Error testing DOCX: {str(e)}")
    else:
        print("   ⚠️  No DOCX files found for testing")
        
except ImportError as e:
    print(f"❌ python-docx not available: {str(e)}")

# Test file access
print("\n📁 Testing file access...")
if os.path.exists("Papa"):
    print("✅ Papa directory exists")
    file_count = 0
    for root, dirs, files in os.walk("Papa"):
        for file in files:
            if file.lower().endswith(('.pdf', '.docx')):
                file_count += 1
    print(f"   Found {file_count} PDF/DOCX files")
else:
    print("❌ Papa directory not found")

print("\n🎯 Summary:")
print("If all tests pass, the enhanced dataset builder should work.")
print("If any tests fail, that's the issue to fix first.")
