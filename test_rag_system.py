#!/usr/bin/env python3
"""
Shyam Trading Company - RAG System Test Suite
=============================================

Comprehensive test suite for validating the RAG system functionality.
Tests document processing, search capabilities, and integration features.

Usage:
    python test_rag_system.py [--verbose] [--source-dir Papa/]

Author: AI Assistant for Shyam Trading Company
Date: 2025
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required packages can be imported"""
    logger.info("Testing imports...")
    
    required_packages = [
        'torch', 'transformers', 'sentence_transformers',
        'faiss', 'numpy', 'pandas', 'spacy',
        'PyPDF2', 'fitz', 'docx', 'mammoth',
        'langchain', 'rank_bm25', 'chromadb'
    ]
    
    failed_imports = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"  ✓ {package}")
        except ImportError as e:
            logger.error(f"  ✗ {package}: {e}")
            failed_imports.append(package)
    
    if failed_imports:
        logger.error(f"Failed to import: {failed_imports}")
        return False
    
    logger.info("All imports successful ✓")
    return True

def test_document_directory(source_dir: str):
    """Test document directory structure and files"""
    logger.info(f"Testing document directory: {source_dir}")
    
    source_path = Path(source_dir)
    
    if not source_path.exists():
        logger.error(f"Source directory does not exist: {source_dir}")
        return False
    
    # Count files by type
    pdf_files = list(source_path.glob("**/*.pdf"))
    docx_files = list(source_path.glob("**/*.docx"))
    
    logger.info(f"  PDF files: {len(pdf_files)}")
    logger.info(f"  DOCX files: {len(docx_files)}")
    
    if len(pdf_files) + len(docx_files) == 0:
        logger.warning("No PDF or DOCX files found")
        return False
    
    # Test file naming conventions
    invoice_files = [f for f in pdf_files + docx_files if f.name.lower().startswith('invoice')]
    account_files = [f for f in pdf_files + docx_files if f.name.lower().startswith('account')]
    quotation_files = [f for f in pdf_files + docx_files if any(f.name.lower().startswith(prefix) for prefix in ['mr.', 'dr.', 'ms.'])]
    
    logger.info(f"  Invoice files: {len(invoice_files)}")
    logger.info(f"  Account files: {len(account_files)}")
    logger.info(f"  Quotation files: {len(quotation_files)}")
    
    logger.info("Document directory test passed ✓")
    return True

def test_rag_system_initialization():
    """Test RAG system initialization"""
    logger.info("Testing RAG system initialization...")
    
    try:
        from shyam_rag_system import ShyamRAGSystem, ShyamRAGConfig
        
        # Test config loading
        config = ShyamRAGConfig()
        logger.info("  ✓ Configuration loaded")
        
        # Test system initialization
        rag = ShyamRAGSystem()
        logger.info("  ✓ RAG system initialized")
        
        return rag
        
    except Exception as e:
        logger.error(f"RAG system initialization failed: {e}")
        return None

def test_document_processing(rag_system, source_dir: str):
    """Test document processing capabilities"""
    logger.info("Testing document processing...")
    
    source_path = Path(source_dir)
    
    # Find a test PDF and DOCX file
    pdf_files = list(source_path.glob("**/*.pdf"))
    docx_files = list(source_path.glob("**/*.docx"))
    
    test_files = []
    if pdf_files:
        test_files.append(pdf_files[0])
    if docx_files:
        test_files.append(docx_files[0])
    
    if not test_files:
        logger.warning("No test files found for processing test")
        return False
    
    for test_file in test_files:
        try:
            logger.info(f"  Processing: {test_file.name}")
            
            chunks = rag_system.document_processor.process_document(str(test_file))
            
            if chunks:
                logger.info(f"    ✓ Generated {len(chunks)} chunks")
                
                # Test metadata
                sample_chunk = chunks[0]
                metadata = sample_chunk.metadata
                
                required_fields = ['file_name', 'document_type', 'file_path']
                for field in required_fields:
                    if field in metadata:
                        logger.info(f"    ✓ Metadata field '{field}': {metadata[field]}")
                    else:
                        logger.warning(f"    ⚠ Missing metadata field: {field}")
            else:
                logger.warning(f"    ⚠ No chunks generated for {test_file.name}")
                
        except Exception as e:
            logger.error(f"    ✗ Processing failed for {test_file.name}: {e}")
            return False
    
    logger.info("Document processing test passed ✓")
    return True

def test_database_building(rag_system, source_dir: str):
    """Test database building process"""
    logger.info("Testing database building...")
    
    try:
        start_time = time.time()
        
        # Build database
        rag_system.build_database(source_dir, force_rebuild=True)
        
        build_time = time.time() - start_time
        logger.info(f"  ✓ Database built in {build_time:.2f} seconds")
        
        # Check if database is ready
        if not rag_system.is_built:
            logger.error("Database not marked as built")
            return False
        
        # Get statistics
        stats = rag_system.get_document_stats()
        logger.info(f"  ✓ Total documents: {stats['total_documents']}")
        logger.info(f"  ✓ Document types: {stats['document_types']}")
        logger.info(f"  ✓ Unique clients: {stats['unique_clients']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Database building failed: {e}")
        return False

def test_search_functionality(rag_system):
    """Test search and query functionality"""
    logger.info("Testing search functionality...")
    
    test_queries = [
        {
            "query": "aluminium door window",
            "description": "Construction materials query"
        },
        {
            "query": "invoice payment amount",
            "description": "Invoice-related query"
        },
        {
            "query": "quotation estimate",
            "description": "Quotation-related query"
        },
        {
            "query": "account statement balance",
            "description": "Account-related query"
        }
    ]
    
    for test_case in test_queries:
        try:
            logger.info(f"  Testing: {test_case['description']}")
            
            start_time = time.time()
            result = rag_system.query(test_case['query'], top_k=3)
            query_time = time.time() - start_time
            
            logger.info(f"    ✓ Query completed in {query_time:.3f} seconds")
            logger.info(f"    ✓ Found {len(result['results'])} results")
            
            # Test result structure
            if result['results']:
                sample_result = result['results'][0]
                required_fields = ['content', 'score', 'metadata', 'source']
                
                for field in required_fields:
                    if field in sample_result:
                        logger.info(f"    ✓ Result field '{field}' present")
                    else:
                        logger.warning(f"    ⚠ Missing result field: {field}")
            
        except Exception as e:
            logger.error(f"    ✗ Query failed: {e}")
            return False
    
    logger.info("Search functionality test passed ✓")
    return True

def test_filtering_capabilities(rag_system):
    """Test metadata filtering capabilities"""
    logger.info("Testing filtering capabilities...")
    
    try:
        # Test document type filtering
        invoice_results = rag_system.query("amount", document_type="invoice", top_k=5)
        logger.info(f"  ✓ Invoice filter: {len(invoice_results['results'])} results")
        
        quotation_results = rag_system.query("estimate", document_type="quotation", top_k=5)
        logger.info(f"  ✓ Quotation filter: {len(quotation_results['results'])} results")
        
        # Test client filtering (if clients exist)
        stats = rag_system.get_document_stats()
        if stats['clients']:
            test_client = stats['clients'][0]
            client_results = rag_system.query("", client_name=test_client, top_k=5)
            logger.info(f"  ✓ Client filter ({test_client}): {len(client_results['results'])} results")
        
        logger.info("Filtering capabilities test passed ✓")
        return True
        
    except Exception as e:
        logger.error(f"Filtering test failed: {e}")
        return False

def test_n8n_integration(rag_system):
    """Test n8n integration format"""
    logger.info("Testing n8n integration...")
    
    try:
        # Get a sample query result
        result = rag_system.query("construction materials", top_k=3)
        
        # Export for n8n
        n8n_result = rag_system.export_for_n8n(result)
        
        # Check n8n format structure
        required_fields = ['success', 'query', 'total_results', 'documents']
        for field in required_fields:
            if field in n8n_result:
                logger.info(f"  ✓ n8n field '{field}' present")
            else:
                logger.error(f"  ✗ Missing n8n field: {field}")
                return False
        
        # Check document structure
        if n8n_result['documents']:
            sample_doc = n8n_result['documents'][0]
            doc_fields = ['content', 'relevance_score', 'source_file', 'document_type']
            
            for field in doc_fields:
                if field in sample_doc:
                    logger.info(f"  ✓ Document field '{field}' present")
                else:
                    logger.warning(f"  ⚠ Missing document field: {field}")
        
        logger.info("n8n integration test passed ✓")
        return True
        
    except Exception as e:
        logger.error(f"n8n integration test failed: {e}")
        return False

def run_comprehensive_test(source_dir: str, verbose: bool = False):
    """Run comprehensive test suite"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print("🧪 Shyam Trading Company - RAG System Test Suite")
    print("=" * 55)
    
    test_results = []
    
    # Test 1: Imports
    test_results.append(("Imports", test_imports()))
    
    # Test 2: Document directory
    test_results.append(("Document Directory", test_document_directory(source_dir)))
    
    # Test 3: RAG system initialization
    rag_system = test_rag_system_initialization()
    test_results.append(("RAG Initialization", rag_system is not None))
    
    if rag_system:
        # Test 4: Document processing
        test_results.append(("Document Processing", test_document_processing(rag_system, source_dir)))
        
        # Test 5: Database building
        test_results.append(("Database Building", test_database_building(rag_system, source_dir)))
        
        if rag_system.is_built:
            # Test 6: Search functionality
            test_results.append(("Search Functionality", test_search_functionality(rag_system)))
            
            # Test 7: Filtering capabilities
            test_results.append(("Filtering Capabilities", test_filtering_capabilities(rag_system)))
            
            # Test 8: n8n integration
            test_results.append(("n8n Integration", test_n8n_integration(rag_system)))
    
    # Print results summary
    print("\n📊 Test Results Summary")
    print("-" * 30)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! RAG system is ready for use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the logs and fix issues.")
        return False

def main():
    parser = argparse.ArgumentParser(description="Test Shyam Trading RAG System")
    parser.add_argument("--source-dir", default="Papa/", help="Source directory for documents")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    success = run_comprehensive_test(args.source_dir, args.verbose)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
