#!/usr/bin/env python3
"""
Comprehensive System Validation for Shyam Trading RAG System
============================================================

This script performs thorough validation of the complete RAG system
with GPU acceleration, business intelligence, and integration testing.
"""

import sys
import os
import time
import json
from datetime import datetime
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_section(title):
    print(f"\n{'='*80}")
    print(f" {title}")
    print(f"{'='*80}")

def test_gpu_acceleration():
    """Test GPU acceleration performance"""
    print_section("GPU ACCELERATION VALIDATION")
    
    try:
        import torch
        from sentence_transformers import SentenceTransformer
        
        print("🔍 GPU Hardware Status:")
        print(f"   CUDA Available: {torch.cuda.is_available()}")
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
        print(f"   GPU Memory: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f} GB")
        
        # Test embedding model GPU usage
        print("\n🔍 Testing Embedding Model GPU Performance:")
        model = SentenceTransformer('sentence-transformers/all-mpnet-base-v2')
        print(f"   Model Device: {model.device}")
        
        # Performance test
        test_texts = [
            "Shyam Trading Company aluminium door quotation",
            "Construction materials invoice payment",
            "Account statement balance verification",
            "ACP cladding work estimate"
        ] * 25  # 100 texts total
        
        start_time = time.time()
        embeddings = model.encode(test_texts, show_progress_bar=False)
        end_time = time.time()
        
        print(f"   ✅ Encoded 100 texts in {end_time - start_time:.3f}s")
        print(f"   Performance: {len(test_texts) / (end_time - start_time):.1f} texts/second")
        print(f"   GPU Memory Used: {torch.cuda.memory_allocated() / (1024**2):.1f} MB")
        
        return True, {
            'gpu_available': torch.cuda.is_available(),
            'encoding_speed': len(test_texts) / (end_time - start_time),
            'gpu_memory_mb': torch.cuda.memory_allocated() / (1024**2)
        }
        
    except Exception as e:
        print(f"❌ GPU acceleration test failed: {e}")
        return False, {}

def test_business_document_processing():
    """Test business-specific document processing"""
    print_section("BUSINESS DOCUMENT PROCESSING VALIDATION")
    
    try:
        from shyam_rag_system import ShyamRAGSystem
        
        rag = ShyamRAGSystem()
        rag.build_database("Papa/", force_rebuild=False)
        
        # Get comprehensive statistics
        stats = rag.get_document_stats()
        
        print("📊 Document Processing Results:")
        print(f"   Total Documents: {stats['total_documents']}")
        print(f"   Document Types:")
        
        for doc_type, count in stats['document_types'].items():
            percentage = (count / stats['total_documents']) * 100
            print(f"      {doc_type.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
        
        print(f"   Unique Clients: {stats['unique_clients']}")
        print(f"   File Types Processed:")
        for file_type, count in stats['file_types'].items():
            print(f"      {file_type.upper()}: {count}")
        
        # Test document classification accuracy
        print("\n🎯 Document Classification Testing:")
        
        classification_tests = [
            ("invoice", "invoice payment amount"),
            ("quotation", "aluminium door quotation estimate"),
            ("account_statement", "account balance statement")
        ]
        
        classification_results = {}
        for expected_type, query in classification_tests:
            result = rag.query(query, document_type=expected_type, top_k=5)
            classification_results[expected_type] = len(result['results'])
            print(f"   {expected_type.title()}: {len(result['results'])} documents found")
        
        return True, {
            'total_documents': stats['total_documents'],
            'document_types': stats['document_types'],
            'unique_clients': stats['unique_clients'],
            'classification_results': classification_results
        }
        
    except Exception as e:
        print(f"❌ Business document processing test failed: {e}")
        return False, {}

def test_search_capabilities():
    """Test comprehensive search capabilities"""
    print_section("SEARCH CAPABILITIES VALIDATION")
    
    try:
        from shyam_rag_system import ShyamRAGSystem
        
        rag = ShyamRAGSystem()
        rag.build_database("Papa/", force_rebuild=False)
        
        # Test different search scenarios
        search_tests = [
            {
                "name": "Construction Materials",
                "query": "aluminium door window ACP cladding",
                "expected_results": "> 5"
            },
            {
                "name": "Financial Documents", 
                "query": "invoice payment amount balance",
                "expected_results": "> 10"
            },
            {
                "name": "Client-Specific",
                "query": "Shyam Trading Company services",
                "expected_results": "> 3"
            },
            {
                "name": "Technical Specifications",
                "query": "track system fitting hardware",
                "expected_results": "> 2"
            },
            {
                "name": "Business Operations",
                "query": "quotation estimate construction work",
                "expected_results": "> 5"
            }
        ]
        
        search_results = {}
        total_search_time = 0
        
        print("🔍 Search Performance Testing:")
        
        for test in search_tests:
            start_time = time.time()
            result = rag.query(test['query'], top_k=10)
            end_time = time.time()
            
            search_time = end_time - start_time
            total_search_time += search_time
            
            search_results[test['name']] = {
                'results_count': len(result['results']),
                'search_time': search_time,
                'top_score': result['results'][0]['score'] if result['results'] else 0
            }
            
            print(f"   {test['name']}: {len(result['results'])} results in {search_time:.3f}s")
            if result['results']:
                print(f"      Top result: {result['results'][0]['metadata']['file_name']}")
                print(f"      Score: {result['results'][0]['score']:.3f}")
        
        avg_search_time = total_search_time / len(search_tests)
        print(f"\n⚡ Average Search Time: {avg_search_time:.3f} seconds")
        
        return True, {
            'search_results': search_results,
            'average_search_time': avg_search_time,
            'total_tests': len(search_tests)
        }
        
    except Exception as e:
        print(f"❌ Search capabilities test failed: {e}")
        return False, {}

def test_hybrid_search_components():
    """Test individual components of hybrid search"""
    print_section("HYBRID SEARCH COMPONENTS VALIDATION")
    
    try:
        from shyam_rag_system import ShyamRAGSystem
        
        rag = ShyamRAGSystem()
        rag.build_database("Papa/", force_rebuild=False)
        
        # Test semantic search
        print("🔍 Testing Search Components:")
        
        test_query = "aluminium door construction"
        
        # Get hybrid results
        result = rag.query(test_query, top_k=5)
        
        print(f"   Hybrid Search Results: {len(result['results'])}")
        if result['results']:
            print(f"   Top Result Score: {result['results'][0]['score']:.3f}")
            print(f"   Source: {result['results'][0]['metadata']['file_name']}")
        
        # Test reranking (if enabled)
        print(f"   Reranking: {'Enabled' if rag.vector_store.reranker else 'Disabled'}")
        
        # Test metadata filtering
        print("\n🎯 Testing Metadata Filtering:")
        
        filter_tests = [
            {"document_type": "quotation"},
            {"document_type": "invoice"},
        ]
        
        for filter_test in filter_tests:
            filtered_result = rag.query(test_query, top_k=3, **filter_test)
            filter_name = list(filter_test.keys())[0]
            filter_value = list(filter_test.values())[0]
            print(f"   {filter_name}={filter_value}: {len(filtered_result['results'])} results")
        
        return True, {
            'hybrid_search_working': len(result['results']) > 0,
            'reranking_enabled': rag.vector_store.reranker is not None,
            'metadata_filtering_working': True
        }
        
    except Exception as e:
        print(f"❌ Hybrid search components test failed: {e}")
        return False, {}

def test_api_integration():
    """Test API integration capabilities"""
    print_section("API INTEGRATION VALIDATION")
    
    try:
        from shyam_rag_system import ShyamRAGSystem
        import fastapi
        
        rag = ShyamRAGSystem()
        rag.build_database("Papa/", force_rebuild=False)
        
        print("🔍 Testing API Components:")
        
        # Test n8n export format
        result = rag.query("construction materials", top_k=3)
        n8n_export = rag.export_for_n8n(result)
        
        print(f"   ✅ n8n Export Format: {n8n_export['success']}")
        print(f"   Documents in Export: {len(n8n_export['documents'])}")
        
        # Test FastAPI availability
        print(f"   ✅ FastAPI Version: {fastapi.__version__}")
        
        # Validate n8n export structure
        required_fields = ['success', 'query', 'total_results', 'documents']
        n8n_valid = all(field in n8n_export for field in required_fields)
        print(f"   ✅ n8n Format Valid: {n8n_valid}")
        
        if n8n_export['documents']:
            sample_doc = n8n_export['documents'][0]
            doc_fields = ['content', 'relevance_score', 'source_file', 'document_type']
            doc_valid = all(field in sample_doc for field in doc_fields)
            print(f"   ✅ Document Format Valid: {doc_valid}")
        
        return True, {
            'n8n_export_working': n8n_export['success'],
            'fastapi_available': True,
            'export_format_valid': n8n_valid
        }
        
    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        return False, {}

def generate_performance_report(test_results):
    """Generate comprehensive performance report"""
    print_section("COMPREHENSIVE PERFORMANCE REPORT")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "system_status": "OPERATIONAL",
        "test_results": test_results,
        "summary": {}
    }
    
    # Calculate summary metrics
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result.get('success', False))
    
    report['summary'] = {
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "success_rate": (passed_tests / total_tests) * 100,
        "overall_status": "PASS" if passed_tests == total_tests else "PARTIAL"
    }
    
    print("📊 SYSTEM PERFORMANCE SUMMARY:")
    print(f"   Tests Passed: {passed_tests}/{total_tests} ({report['summary']['success_rate']:.1f}%)")
    print(f"   Overall Status: {report['summary']['overall_status']}")
    
    # GPU Performance
    if 'gpu_acceleration' in test_results and test_results['gpu_acceleration']['success']:
        gpu_data = test_results['gpu_acceleration']['data']
        print(f"\n⚡ GPU Performance:")
        print(f"   Encoding Speed: {gpu_data.get('encoding_speed', 0):.1f} texts/second")
        print(f"   GPU Memory Usage: {gpu_data.get('gpu_memory_mb', 0):.1f} MB")
    
    # Document Processing
    if 'document_processing' in test_results and test_results['document_processing']['success']:
        doc_data = test_results['document_processing']['data']
        print(f"\n📄 Document Processing:")
        print(f"   Total Documents: {doc_data.get('total_documents', 0)}")
        print(f"   Unique Clients: {doc_data.get('unique_clients', 0)}")
    
    # Search Performance
    if 'search_capabilities' in test_results and test_results['search_capabilities']['success']:
        search_data = test_results['search_capabilities']['data']
        print(f"\n🔍 Search Performance:")
        print(f"   Average Search Time: {search_data.get('average_search_time', 0):.3f}s")
        print(f"   Search Tests Passed: {search_data.get('total_tests', 0)}")
    
    # Save report
    with open("system_performance_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📁 Report saved to: system_performance_report.json")
    
    return report

def main():
    """Main validation function"""
    print("🧪 Shyam Trading RAG System - Comprehensive Validation")
    print("Testing GPU-accelerated production system...")
    
    test_results = {}
    
    # Run all validation tests
    print("\n🚀 Starting Comprehensive System Validation...")
    
    # Test 1: GPU Acceleration
    success, data = test_gpu_acceleration()
    test_results['gpu_acceleration'] = {'success': success, 'data': data}
    
    # Test 2: Business Document Processing
    success, data = test_business_document_processing()
    test_results['document_processing'] = {'success': success, 'data': data}
    
    # Test 3: Search Capabilities
    success, data = test_search_capabilities()
    test_results['search_capabilities'] = {'success': success, 'data': data}
    
    # Test 4: Hybrid Search Components
    success, data = test_hybrid_search_components()
    test_results['hybrid_search'] = {'success': success, 'data': data}
    
    # Test 5: API Integration
    success, data = test_api_integration()
    test_results['api_integration'] = {'success': success, 'data': data}
    
    # Generate comprehensive report
    report = generate_performance_report(test_results)
    
    # Final status
    if report['summary']['overall_status'] == "PASS":
        print("\n🎉 ALL VALIDATIONS PASSED!")
        print("✅ GPU-accelerated RAG system is production-ready")
        print("✅ Ready for integration with fine-tuned model")
        print("✅ Ready for n8n workflow integration")
        return True
    else:
        print(f"\n⚠️  {report['summary']['passed_tests']}/{report['summary']['total_tests']} validations passed")
        print("Some components may need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
