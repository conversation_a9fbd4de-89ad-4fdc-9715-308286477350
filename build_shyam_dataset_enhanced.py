"""
Shyam Trading Company - Enhanced Dataset Builder
===============================================

This script processes business documents (PDFs and DOCX files) from Shyam Trading Company
and creates a structured dataset for fine-tuning a language model.

Enhanced Features:
- Improved PDF text extraction with multiple fallback methods
- Better customer name recognition for Indian business formats
- Enhanced document classification for construction/architectural industry
- Robust handling of various document layouts and formats
- Detailed logging and error tracking

Usage:
    python build_shyam_dataset_enhanced.py

Output:
    - shyam_finetune.jsonl: Training data in JSONL format
    - dataset_errors.log: Processing errors
    - dataset_warnings.log: Processing warnings
"""

import os
import re
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import traceback

# PDF and document processing
try:
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    print("⚠️  pdfplumber not available. Install with: pip install pdfplumber")
    PDF_AVAILABLE = False

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    print("⚠️  python-docx not available. Install with: pip install python-docx")
    DOCX_AVAILABLE = False

# Setup enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create separate loggers for errors and warnings
error_logger = logging.getLogger('errors')
error_handler = logging.FileHandler('dataset_errors.log', mode='w', encoding='utf-8')
error_handler.setLevel(logging.ERROR)
error_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
error_handler.setFormatter(error_formatter)
error_logger.addHandler(error_handler)

warning_logger = logging.getLogger('warnings')
warning_handler = logging.FileHandler('dataset_warnings.log', mode='w', encoding='utf-8')
warning_handler.setLevel(logging.WARNING)
warning_handler.setFormatter(error_formatter)
warning_logger.addHandler(warning_handler)

def extract_text_from_pdf(file_path: str) -> str:
    """Enhanced PDF text extraction with multiple fallback methods"""
    text = ""
    
    try:
        # Method 1: pdfplumber (best for structured documents)
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        
        if text.strip():
            logger.debug(f"Successfully extracted text using pdfplumber: {file_path}")
            return text
            
    except Exception as e:
        warning_logger.warning(f"pdfplumber failed for {file_path}: {str(e)}")
    
    # Method 2: Try alternative extraction if pdfplumber fails
    try:
        import PyPDF2
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        
        if text.strip():
            logger.debug(f"Successfully extracted text using PyPDF2: {file_path}")
            return text
            
    except Exception as e:
        warning_logger.warning(f"PyPDF2 also failed for {file_path}: {str(e)}")
    
    # If all methods fail, return empty string
    error_logger.error(f"All PDF extraction methods failed for: {file_path}")
    return ""

def extract_text_from_docx(file_path: str) -> str:
    """Extract text from DOCX files"""
    try:
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        
        # Also extract text from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    text += cell.text + " "
                text += "\n"
        
        logger.debug(f"Successfully extracted text from DOCX: {file_path}")
        return text
        
    except Exception as e:
        error_logger.error(f"Failed to extract text from DOCX {file_path}: {str(e)}")
        return ""

def classify_document_type(filename: str, text: str) -> str:
    """Enhanced document classification for Shyam Trading Company documents"""
    filename_lower = filename.lower()
    text_lower = text.lower()
    
    # Invoice patterns
    if (any(pattern in filename_lower for pattern in ['invoice', 'bill', 'tax invoice']) or
        any(pattern in text_lower for pattern in ['tax invoice', 'invoice no', 'bill no', 'gstin'])):
        return 'invoice'
    
    # Quotation patterns (enhanced for your business)
    if (any(pattern in filename_lower for pattern in ['quotation', 'quote', 'mr.', 'mr ', 'mrs.', 'dr.', 'ms.']) or
        any(pattern in text_lower for pattern in ['quotation', 'quote', 'estimate', 'proposal'])):
        return 'quotation'
    
    # Account statement patterns
    if (any(pattern in filename_lower for pattern in ['account', 'statement', 'ledger']) or
        any(pattern in text_lower for pattern in ['account statement', 'ledger', 'balance', 'transaction'])):
        return 'account_statement'
    
    # Receipt patterns
    if (any(pattern in filename_lower for pattern in ['receipt', 'received']) or
        any(pattern in text_lower for pattern in ['received with thanks', 'receipt', 'payment received'])):
        return 'receipt'
    
    # Work order patterns
    if (any(pattern in filename_lower for pattern in ['work order', 'wo ', 'order']) or
        any(pattern in text_lower for pattern in ['work order', 'purchase order', 'po no'])):
        return 'work_order'
    
    # Default classification based on content
    if any(pattern in text_lower for pattern in ['amount', 'total', 'payment', 'due']):
        return 'financial_document'
    
    return 'unknown'

def extract_customer_name(text: str, doc_type: str, filename: str) -> Optional[str]:
    """Enhanced customer name extraction for Indian business documents"""
    text_lines = text.split('\n')
    
    # Enhanced patterns based on actual Shyam Trading document formats
    name_patterns = [
        # Standard billing patterns
        r'(?:To|TO|Bill\s+To|BILL\s+TO|Customer|CUSTOMER)[\s:]*([A-Za-z\s\.&,\-\(\)]+)',
        r'(?:Mr\.|Mrs\.|Ms\.|Dr\.|M/s|Shri|Sri)[\s]*([A-Za-z\s\.&,\-\(\)]+)',
        r'(?:Name|NAME)[\s:]*([A-Za-z\s\.&,\-\(\)]+)',
        
        # Educational institutions (common in your docs)
        r'([A-Z][a-z]+\s+[A-Z][a-z]+\s+(?:School|College|Institute|Academy|University))',
        r'([A-Z][a-z]+\s+(?:School|College|Institute|Academy|University))',
        r'(Mount\s+[A-Z][a-z]+\s+School)',
        
        # Companies and organizations
        r'([A-Z][a-z]+\s+[A-Z][a-z]+\s+(?:Pvt\.?\s*Ltd\.?|Limited|Company|Corp\.?|Co\.?))',
        r'([A-Z][a-z]+\s+(?:Pvt\.?\s*Ltd\.?|Limited|Company|Corp\.?|Co\.?))',
        r'([A-Z][a-z]+\s+[A-Z][a-z]+\s+(?:Industries|Enterprises|Solutions|Services|Trading))',
        
        # Infrastructure/Construction companies (your domain)
        r'([A-Z][a-z]+\s+[A-Z][a-z]+\s+(?:Infrastructure|Construction|Builders?|Buildcon))',
        r'([A-Z][a-z]+\s+(?:Infrastructure|Construction|Builders?|Buildcon))',
        r'(DP\s+Jain\s+Infrastructure)',
        
        # Hospital and medical
        r'([A-Z][a-z]+\s+Hospital)',
        r'(Dr\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',
        
        # General name patterns
        r'^([A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',  # 2-3 word names
        r'([A-Z]{2,}\s+[A-Z]{2,})',  # All caps names
        
        # Address-based extraction
        r'(?:Address|ADDRESS)[\s:]*([A-Za-z\s\.&,\-\(\)]+?)(?:\n|,\s*[0-9])',
    ]
    
    # Extract from filename first (often contains customer name)
    filename_clean = os.path.splitext(filename)[0]
    filename_patterns = [
        r'Mr\.?\s+([A-Za-z\s]+)',
        r'Mrs\.?\s+([A-Za-z\s]+)',
        r'Dr\.?\s+([A-Za-z\s]+)',
        r'([A-Z][a-z]+\s+[A-Z][a-z]+)',
    ]
    
    for pattern in filename_patterns:
        match = re.search(pattern, filename_clean)
        if match:
            name = match.group(1).strip()
            if len(name) > 2 and len(name) < 50:
                return clean_name(name)
    
    # Search in document text (first 20 lines for header info)
    for i, line in enumerate(text_lines[:20]):
        line = line.strip()
        if not line or len(line) < 3:
            continue
            
        for pattern in name_patterns:
            match = re.search(pattern, line)
            if match:
                name = match.group(1).strip()
                cleaned_name = clean_name(name)
                if is_valid_name(cleaned_name):
                    return cleaned_name
    
    # For account statements, look for transaction party names
    if doc_type == 'account_statement':
        for line in text_lines[5:30]:
            line = line.strip()
            # Look for transaction party names
            patterns = [
                r'(?:paid|received|from|to)[\s:]+([A-Za-z\s\.&,\-\(\)]+)',
                r'([A-Z][a-z]+\s+[A-Z][a-z]+)(?:\s+(?:paid|received))',
            ]
            for pattern in patterns:
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    name = clean_name(match.group(1))
                    if is_valid_name(name):
                        return name
    
    return None

def clean_name(name: str) -> str:
    """Clean and normalize extracted names"""
    if not name:
        return ""
    
    # Remove extra whitespace
    name = re.sub(r'\s+', ' ', name).strip()
    
    # Remove trailing punctuation
    name = name.strip('.,:-()[]{}')
    
    # Remove common suffixes that aren't part of names
    suffixes_to_remove = [
        'invoice', 'quotation', 'bill', 'receipt', 'statement',
        'date', 'amount', 'total', 'gst', 'tax', 'address'
    ]
    
    for suffix in suffixes_to_remove:
        if name.lower().endswith(suffix):
            name = name[:-len(suffix)].strip()
    
    return name

def is_valid_name(name: str) -> bool:
    """Validate if extracted text is a valid customer name"""
    if not name or len(name) < 2 or len(name) > 80:
        return False
    
    # Check for invalid patterns
    invalid_patterns = [
        r'\d{4,}',  # Long numbers
        r'(?:invoice|quotation|receipt|date|amount|total|gst|tax|address|phone|mobile)',
        r'^[0-9\s\-\.\,]+$',  # Only numbers and punctuation
        r'[^\w\s\.\&\,\-\(\)]',  # Invalid characters
    ]
    
    for pattern in invalid_patterns:
        if re.search(pattern, name.lower()):
            return False
    
    # Must have at least one meaningful word (length > 1)
    meaningful_words = [w for w in name.split() if len(w) > 1]
    if len(meaningful_words) < 1:
        return False
    
    return True

def extract_date(text: str) -> Optional[str]:
    """Extract date from document text"""
    date_patterns = [
        r'(?:Date|DATE)[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})',
        r'(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})',
        r'(\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4})',
    ]

    for pattern in date_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1)

    return None

def extract_amount(text: str) -> Optional[float]:
    """Extract total amount from document"""
    amount_patterns = [
        r'(?:Total|TOTAL|Grand\s+Total)[\s:]*(?:Rs\.?|₹)?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
        r'(?:Amount|AMOUNT)[\s:]*(?:Rs\.?|₹)?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
        r'₹\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
        r'Rs\.?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
    ]

    for pattern in amount_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Take the last (usually largest) amount found
            amount_str = matches[-1].replace(',', '')
            try:
                return float(amount_str)
            except ValueError:
                continue

    return None

def extract_line_items(text: str, doc_type: str) -> List[Dict]:
    """Extract line items from document"""
    items = []
    lines = text.split('\n')

    # Look for table-like structures
    for i, line in enumerate(lines):
        line = line.strip()

        # Skip empty lines and headers
        if not line or len(line) < 10:
            continue

        # Look for lines with item descriptions and amounts
        # Pattern: Description ... Quantity ... Rate ... Amount
        if re.search(r'(?:window|door|glass|aluminum|steel|frame|panel)', line.lower()):
            # This looks like a product line
            item = {
                'description': line,
                'line_number': len(items) + 1
            }

            # Try to extract quantity and amount from the same line
            qty_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:nos?|pcs?|sq\.?ft|sqft)', line.lower())
            if qty_match:
                item['quantity'] = float(qty_match.group(1))

            amount_match = re.search(r'(?:rs\.?|₹)\s*(\d+(?:,\d{3})*(?:\.\d{2})?)', line.lower())
            if amount_match:
                item['amount'] = float(amount_match.group(1).replace(',', ''))

            items.append(item)

    return items[:10]  # Limit to first 10 items

def create_training_prompt(doc_type: str, customer_name: str, date: str,
                          amount: float, items: List[Dict], filename: str) -> str:
    """Create training prompt for the document"""

    if doc_type == 'invoice':
        prompt = f"""You are Shyam Trading Company's Invoice Generator. Given the fields below, output a JSON that exactly matches our standard invoice schema:

CUSTOMER_NAME: "{customer_name}"
DOCUMENT_DATE: "{date}"
TOTAL_AMOUNT: "{amount}"
LINE_ITEMS: {json.dumps(items) if items else "[]"}

Now generate the JSON output that includes all of these fields under their corresponding keys.
Output EXACTLY one JSON object—no extra commentary or markdown."""

    elif doc_type == 'quotation':
        prompt = f"""You are Shyam Trading Company's Quotation Generator. Given the fields below, output a JSON that exactly matches our standard quotation schema:

CUSTOMER_NAME: "{customer_name}"
QUOTATION_DATE: "{date}"
QUOTED_AMOUNT: "{amount}"
ITEMS: {json.dumps(items) if items else "[]"}

Now generate the JSON output with these exact keys—no extra text."""

    elif doc_type == 'account_statement':
        prompt = f"""You are Shyam Trading Company's Account Statement Generator. Given the fields below, output a JSON that exactly matches our standard account statement schema:

ACCOUNT_HOLDER: "{customer_name}"
STATEMENT_DATE: "{date}"
BALANCE_AMOUNT: "{amount}"
TRANSACTIONS: {json.dumps(items) if items else "[]"}

Now generate the JSON output with these exact keys—no extra text."""

    else:
        prompt = f"""You are Shyam Trading Company's Document Generator. Given the fields below, output a JSON that matches our standard document schema:

CUSTOMER_NAME: "{customer_name}"
DOCUMENT_DATE: "{date}"
AMOUNT: "{amount}"
DOCUMENT_TYPE: "{doc_type}"
ITEMS: {json.dumps(items) if items else "[]"}

Now generate the JSON output with these exact keys—no extra text."""

    return prompt

def create_training_completion(doc_type: str, customer_name: str, date: str,
                              amount: float, items: List[Dict], filename: str) -> str:
    """Create training completion (expected output) for the document"""

    if doc_type == 'invoice':
        completion = {
            "document_type": "invoice",
            "customer": {
                "name": customer_name,
                "address": "",
                "gstin": ""
            },
            "invoice_number": "",
            "document_date": date,
            "due_date": "",
            "line_items": items,
            "subtotal": amount * 0.85 if amount else 0,  # Estimate subtotal
            "taxes": [
                {
                    "type": "GST 18%",
                    "amount": amount * 0.15 if amount else 0
                }
            ],
            "total": amount,
            "footer": {
                "payment_terms": "Payment due within 30 days",
                "bank_details": "Punjab National Bank",
                "signature": "For Shyam Trading Company"
            }
        }

    elif doc_type == 'quotation':
        completion = {
            "document_type": "quotation",
            "customer": {
                "name": customer_name,
                "address": "",
                "contact": ""
            },
            "quotation_number": "",
            "quotation_date": date,
            "valid_until": "",
            "items": items,
            "subtotal": amount * 0.85 if amount else 0,
            "taxes": [
                {
                    "type": "GST 18%",
                    "amount": amount * 0.15 if amount else 0
                }
            ],
            "total": amount,
            "terms": {
                "payment": "50% advance, balance on delivery",
                "delivery": "As per requirement",
                "warranty": "1 year manufacturing defect"
            }
        }

    elif doc_type == 'account_statement':
        completion = {
            "document_type": "account_statement",
            "account_holder": customer_name,
            "statement_period": date,
            "opening_balance": 0,
            "transactions": items,
            "closing_balance": amount,
            "summary": {
                "total_debits": 0,
                "total_credits": amount,
                "transaction_count": len(items)
            }
        }

    else:
        completion = {
            "document_type": doc_type,
            "customer_name": customer_name,
            "document_date": date,
            "amount": amount,
            "items": items
        }

    return json.dumps(completion, indent=2)

def process_document(file_path: str) -> Optional[Dict]:
    """Process a single document and extract training data"""
    filename = os.path.basename(file_path)

    try:
        # Extract text based on file type
        if file_path.lower().endswith('.pdf'):
            text = extract_text_from_pdf(file_path)
        elif file_path.lower().endswith('.docx'):
            text = extract_text_from_docx(file_path)
        else:
            warning_logger.warning(f"Unsupported file type: {file_path}")
            return None

        if not text.strip():
            warning_logger.warning(f"No text extracted from: {file_path}")
            return None

        # Classify document type
        doc_type = classify_document_type(filename, text)

        # Extract key information
        customer_name = extract_customer_name(text, doc_type, filename)
        if not customer_name:
            warning_logger.warning(f"No customer name found in: {file_path}")
            return None

        date = extract_date(text)
        amount = extract_amount(text)
        items = extract_line_items(text, doc_type)

        # Create training data
        prompt = create_training_prompt(doc_type, customer_name, date or "Not specified",
                                       amount or 0, items, filename)
        completion = create_training_completion(doc_type, customer_name, date or "Not specified",
                                              amount or 0, items, filename)

        return {
            'prompt': prompt,
            'completion': completion,
            'metadata': {
                'filename': filename,
                'doc_type': doc_type,
                'customer_name': customer_name,
                'date': date,
                'amount': amount,
                'items_count': len(items)
            }
        }

    except Exception as e:
        error_logger.error(f"Error processing {file_path}: {str(e)}\n{traceback.format_exc()}")
        return None

def main():
    """Main function to process all documents and create training dataset"""

    print("🚀 Shyam Trading Company - Enhanced Dataset Builder")
    print("=" * 60)

    # Configuration
    documents_dir = "Papa"  # Directory containing the documents
    output_file = "shyam_finetune.jsonl"

    if not os.path.exists(documents_dir):
        print(f"❌ Documents directory '{documents_dir}' not found!")
        print("Please ensure your documents are in the 'Papa' directory.")
        return

    # Find all PDF and DOCX files
    supported_extensions = ['.pdf', '.docx']
    all_files = []

    for root, dirs, files in os.walk(documents_dir):
        for file in files:
            if any(file.lower().endswith(ext) for ext in supported_extensions):
                all_files.append(os.path.join(root, file))

    print(f"📁 Found {len(all_files)} documents to process")

    # Process documents
    training_data = []
    processed_count = 0
    success_count = 0

    for file_path in all_files:
        processed_count += 1
        filename = os.path.basename(file_path)

        print(f"📄 Processing ({processed_count}/{len(all_files)}): {filename}")

        result = process_document(file_path)
        if result:
            training_data.append(result)
            success_count += 1
            print(f"   ✅ Success - Customer: {result['metadata']['customer_name']}")
        else:
            print(f"   ❌ Failed - Check logs for details")

    # Save training data
    if training_data:
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in training_data:
                json.dump({
                    'prompt': item['prompt'],
                    'completion': item['completion']
                }, f, ensure_ascii=False)
                f.write('\n')

        print(f"\n🎉 Dataset creation completed!")
        print(f"📊 Results:")
        print(f"   - Total files processed: {processed_count}")
        print(f"   - Successful extractions: {success_count}")
        print(f"   - Success rate: {success_count/processed_count*100:.1f}%")
        print(f"   - Output file: {output_file}")

        # Show sample of extracted data
        print(f"\n📋 Sample extracted data:")
        for i, item in enumerate(training_data[:3]):
            meta = item['metadata']
            print(f"   {i+1}. {meta['filename']}")
            print(f"      Type: {meta['doc_type']}")
            print(f"      Customer: {meta['customer_name']}")
            print(f"      Date: {meta['date']}")
            print(f"      Amount: ₹{meta['amount']}" if meta['amount'] else "      Amount: Not found")
            print(f"      Items: {meta['items_count']}")
            print()

        print(f"📝 Check logs for detailed processing information:")
        print(f"   - Errors: dataset_errors.log")
        print(f"   - Warnings: dataset_warnings.log")

    else:
        print("❌ No training data could be extracted!")
        print("Please check the error logs and document formats.")

if __name__ == "__main__":
    main()
