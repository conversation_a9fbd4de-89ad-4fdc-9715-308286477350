"""
Test script to examine document extraction and understand formats
"""

import os
import pdfplumber
from docx import Document

def test_pdf_extraction(file_path):
    """Test PDF text extraction and show results"""
    print(f"\n🔍 Testing PDF: {os.path.basename(file_path)}")
    print("-" * 50)
    
    try:
        with pdfplumber.open(file_path) as pdf:
            for i, page in enumerate(pdf.pages):
                text = page.extract_text()
                if text:
                    print(f"Page {i+1} text (first 500 chars):")
                    print(text[:500])
                    print("...")
                    break
    except Exception as e:
        print(f"Error: {e}")

def test_docx_extraction(file_path):
    """Test DOCX text extraction and show results"""
    print(f"\n🔍 Testing DOCX: {os.path.basename(file_path)}")
    print("-" * 50)
    
    try:
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        
        print(f"Document text (first 500 chars):")
        print(text[:500])
        print("...")
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Test a few sample documents"""
    
    # Test some sample files
    test_files = [
        "Papa/Mr. <PERSON>itya.pdf",
        "Papa/Account Statement (DP Jain) .pdf", 
        "Papa/Mount Caramel School .pdf",
        "Papa/Mr. Aditya.docx",
        "Papa/Account Statement (DP Jain) .docx"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            if file_path.endswith('.pdf'):
                test_pdf_extraction(file_path)
            elif file_path.endswith('.docx'):
                test_docx_extraction(file_path)
        else:
            print(f"❌ File not found: {file_path}")

if __name__ == "__main__":
    main()
