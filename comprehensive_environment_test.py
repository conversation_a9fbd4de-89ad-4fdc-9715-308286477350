#!/usr/bin/env python3
"""
Comprehensive Environment Test for Shyam Trading RAG System
==========================================================

This script performs a thorough test of all components with GPU acceleration
to ensure the RAG system is ready for production use.
"""

import sys
import os
import time
from pathlib import Path

def print_section(title):
    print(f"\n{'='*70}")
    print(f" {title}")
    print(f"{'='*70}")

def test_gpu_pytorch():
    print_section("GPU PYTORCH TEST")
    
    try:
        import torch
        print(f"✅ PyTorch Version: {torch.__version__}")
        print(f"CUDA Available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"CUDA Version: {torch.version.cuda}")
            print(f"GPU Count: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
            
            # Test GPU performance
            device = torch.device('cuda')
            x = torch.randn(1000, 1000, device=device)
            y = torch.randn(1000, 1000, device=device)
            
            start_time = time.time()
            for _ in range(50):
                z = torch.matmul(x, y)
            torch.cuda.synchronize()
            end_time = time.time()
            
            print(f"✅ GPU Performance: {end_time - start_time:.3f}s for 50 matrix ops")
            print(f"GPU Memory Used: {torch.cuda.memory_allocated() / (1024**2):.1f} MB")
            
            return True
        else:
            print("❌ CUDA not available")
            return False
            
    except Exception as e:
        print(f"❌ PyTorch GPU test failed: {e}")
        return False

def test_transformers_gpu():
    print_section("TRANSFORMERS GPU TEST")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA not available, skipping GPU test")
            return False
        
        print("🔄 Loading small transformer model for GPU test...")
        
        # Use a small model for testing
        model_name = "sentence-transformers/all-MiniLM-L6-v2"
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModel.from_pretrained(model_name)
        
        # Move to GPU
        device = torch.device('cuda')
        model = model.to(device)
        
        # Test inference
        test_text = "This is a test sentence for GPU acceleration."
        inputs = tokenizer(test_text, return_tensors="pt", padding=True, truncation=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        start_time = time.time()
        with torch.no_grad():
            outputs = model(**inputs)
        torch.cuda.synchronize()
        end_time = time.time()
        
        print(f"✅ Transformer GPU inference: {end_time - start_time:.3f}s")
        print(f"Output shape: {outputs.last_hidden_state.shape}")
        print(f"GPU Memory Used: {torch.cuda.memory_allocated() / (1024**2):.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Transformers GPU test failed: {e}")
        return False

def test_sentence_transformers():
    print_section("SENTENCE TRANSFORMERS TEST")
    
    try:
        from sentence_transformers import SentenceTransformer
        import torch
        
        print("🔄 Loading sentence transformer model...")
        
        # Load model
        model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Check if it uses GPU
        device = model.device
        print(f"Model device: {device}")
        
        # Test encoding
        test_sentences = [
            "Shyam Trading Company provides construction solutions",
            "Aluminium doors and windows quotation",
            "Invoice for construction materials"
        ]
        
        start_time = time.time()
        embeddings = model.encode(test_sentences)
        end_time = time.time()
        
        print(f"✅ Sentence encoding: {end_time - start_time:.3f}s")
        print(f"Embeddings shape: {embeddings.shape}")
        
        if torch.cuda.is_available():
            print(f"GPU Memory Used: {torch.cuda.memory_allocated() / (1024**2):.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Sentence Transformers test failed: {e}")
        return False

def test_document_processing():
    print_section("DOCUMENT PROCESSING TEST")
    
    try:
        # Test PDF processing
        import PyPDF2
        import fitz  # PyMuPDF
        print("✅ PDF processing libraries available")
        
        # Test DOCX processing
        from docx import Document
        import mammoth
        print("✅ DOCX processing libraries available")
        
        # Test spaCy
        import spacy
        nlp = spacy.load("en_core_web_sm")
        
        test_text = "Shyam Trading Company, established in 1985, provides construction solutions in Nagpur."
        doc = nlp(test_text)
        
        entities = [(ent.text, ent.label_) for ent in doc.ents]
        print(f"✅ spaCy NLP processing: Found {len(entities)} entities")
        for entity, label in entities:
            print(f"   {entity}: {label}")
        
        return True
        
    except Exception as e:
        print(f"❌ Document processing test failed: {e}")
        return False

def test_vector_search():
    print_section("VECTOR SEARCH TEST")
    
    try:
        import faiss
        import numpy as np
        from rank_bm25 import BM25Okapi
        
        # Test FAISS
        dimension = 384  # MiniLM embedding dimension
        index = faiss.IndexFlatIP(dimension)
        
        # Create test vectors
        test_vectors = np.random.random((100, dimension)).astype('float32')
        index.add(test_vectors)
        
        # Test search
        query_vector = np.random.random((1, dimension)).astype('float32')
        scores, indices = index.search(query_vector, 5)
        
        print(f"✅ FAISS vector search: Found {len(indices[0])} results")
        
        # Test BM25
        test_documents = [
            "aluminium door window construction",
            "invoice payment amount trading",
            "quotation estimate building materials",
            "account statement balance ledger"
        ]
        
        tokenized_docs = [doc.split() for doc in test_documents]
        bm25 = BM25Okapi(tokenized_docs)
        
        query = "aluminium construction"
        scores = bm25.get_scores(query.split())
        
        print(f"✅ BM25 keyword search: Scores computed for {len(scores)} documents")
        
        return True
        
    except Exception as e:
        print(f"❌ Vector search test failed: {e}")
        return False

def test_api_components():
    print_section("API COMPONENTS TEST")
    
    try:
        import fastapi
        import uvicorn
        print(f"✅ FastAPI: {fastapi.__version__}")
        
        # Test basic FastAPI app creation
        app = fastapi.FastAPI(title="Test API")
        
        @app.get("/test")
        def test_endpoint():
            return {"status": "ok"}
        
        print("✅ FastAPI app creation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ API components test failed: {e}")
        return False

def test_papa_directory():
    print_section("PAPA DIRECTORY TEST")
    
    papa_dir = Path("Papa")
    
    if not papa_dir.exists():
        print("❌ Papa directory not found")
        return False
    
    # Count files
    pdf_files = list(papa_dir.glob("**/*.pdf"))
    docx_files = list(papa_dir.glob("**/*.docx"))
    
    print(f"✅ Papa directory found")
    print(f"PDF files: {len(pdf_files)}")
    print(f"DOCX files: {len(docx_files)}")
    print(f"Total documents: {len(pdf_files) + len(docx_files)}")
    
    # Show sample files
    all_files = pdf_files + docx_files
    if all_files:
        print("\nSample files:")
        for i, file_path in enumerate(all_files[:5]):
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"   {i+1}. {file_path.name} ({size_mb:.1f} MB)")
        
        if len(all_files) > 5:
            print(f"   ... and {len(all_files) - 5} more files")
    
    return len(all_files) > 0

def main():
    print("🔍 Shyam Trading RAG System - Comprehensive Environment Test")
    print("Testing GPU-accelerated RAG system components...")
    
    test_results = {}
    
    # Run all tests
    test_results['GPU PyTorch'] = test_gpu_pytorch()
    test_results['Transformers GPU'] = test_transformers_gpu()
    test_results['Sentence Transformers'] = test_sentence_transformers()
    test_results['Document Processing'] = test_document_processing()
    test_results['Vector Search'] = test_vector_search()
    test_results['API Components'] = test_api_components()
    test_results['Papa Directory'] = test_papa_directory()
    
    # Summary
    print_section("TEST RESULTS SUMMARY")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("Your GPU-accelerated RAG system is ready for production!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        print("Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
