#!/usr/bin/env python3
"""
Setup Progress Checker for Shyam Trading RAG System
===================================================

This script checks the progress of the setup process by monitoring
the log file and providing real-time status updates.
"""

import os
import time
from pathlib import Path
from datetime import datetime

def check_setup_progress():
    log_file = "shyam_rag_setup.log"
    
    if not Path(log_file).exists():
        print("❌ Setup log file not found. Setup may not have started.")
        return
    
    print("🔍 Checking Shyam Trading RAG Setup Progress...")
    print("=" * 60)
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        if not lines:
            print("📝 Log file is empty. Setup may be starting...")
            return
        
        # Parse the log for key information
        packages_installed = []
        current_package = None
        errors = []
        
        for line in lines:
            line = line.strip()
            
            # Check for package installation completion
            if "Package group" in line and "installed successfully" in line:
                packages_installed.append(line)
            
            # Check for current package being installed
            if "Installing package group" in line:
                current_package = line
            
            # Check for errors
            if "ERROR" in line or "Failed" in line:
                errors.append(line)
        
        # Display progress
        print(f"📊 Setup Progress Report (as of {datetime.now().strftime('%H:%M:%S')})")
        print("-" * 60)
        
        if packages_installed:
            print("✅ Completed Package Groups:")
            for pkg in packages_installed:
                print(f"   {pkg}")
        
        if current_package:
            print(f"\n🔄 Currently Installing:")
            print(f"   {current_package}")
        
        if errors:
            print(f"\n⚠️  Errors Found:")
            for error in errors:
                print(f"   {error}")
        
        # Show last few lines for context
        print(f"\n📝 Last 5 Log Entries:")
        for line in lines[-5:]:
            print(f"   {line.strip()}")
        
        # Check if setup is complete
        if "Setup completed successfully" in ''.join(lines):
            print(f"\n🎉 SETUP COMPLETED SUCCESSFULLY!")
        elif "Setup failed" in ''.join(lines):
            print(f"\n❌ SETUP FAILED!")
        else:
            print(f"\n⏳ Setup is still in progress...")
            print(f"   Monitor the log file: {log_file}")
        
    except Exception as e:
        print(f"❌ Error reading log file: {e}")

def check_installed_packages():
    """Check what packages are currently installed in the virtual environment"""
    print("\n" + "=" * 60)
    print("📦 Checking Installed Packages in Virtual Environment")
    print("=" * 60)
    
    venv_python = Path("shyam_rag_env") / "Scripts" / "python.exe"
    
    if not venv_python.exists():
        print("❌ Virtual environment Python not found")
        return
    
    try:
        import subprocess
        result = subprocess.run([str(venv_python), "-m", "pip", "list"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Installed packages:")
            print(result.stdout)
        else:
            print(f"❌ Error checking packages: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    check_setup_progress()
    check_installed_packages()
    
    print("\n" + "=" * 60)
    print("💡 Tips:")
    print("   - If setup is still running, be patient (PyTorch takes time)")
    print("   - Check shyam_rag_setup.log for detailed progress")
    print("   - Run this script again to check updated progress")
    print("=" * 60)

if __name__ == "__main__":
    main()
