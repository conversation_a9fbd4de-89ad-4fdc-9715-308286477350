2025-07-18 01:23:32,788 - faiss.loader - INFO - Loading faiss with AVX2 support.
2025-07-18 01:23:32,808 - faiss.loader - INFO - Successfully loaded faiss with AVX2 support.
2025-07-18 01:23:32,813 - faiss - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-18 01:23:33,346 - __main__ - INFO - Initializing GPU-accelerated RAG system...
2025-07-18 01:23:33,767 - shyam_rag_system - INFO - Loading embedding model: sentence-transformers/all-mpnet-base-v2
2025-07-18 01:23:33,770 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda:0
2025-07-18 01:23:33,770 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-mpnet-base-v2
2025-07-18 01:23:41,004 - shyam_rag_system - INFO - Loading reranker model: cross-encoder/ms-marco-MiniLM-L-6-v2
2025-07-18 01:23:43,147 - sentence_transformers.cross_encoder.CrossEncoder - INFO - Use pytorch device: cuda:0
2025-07-18 01:23:43,698 - __main__ - INFO - Starting comprehensive database build...
2025-07-18 01:23:43,699 - shyam_rag_system - INFO - Building RAG database from Papa/
2025-07-18 01:23:43,724 - shyam_rag_system - INFO - Found 1415 documents to process
2025-07-18 01:23:48,304 - shyam_rag_system - WARNING - No content extracted from Papa\DOC-20241230-WA0022_241230_181749.pdf
2025-07-18 01:23:50,947 - shyam_rag_system - WARNING - No content extracted from Papa\HYT Alu window.pdf
2025-07-18 01:23:51,987 - shyam_rag_system - WARNING - No content extracted from Papa\HYT Workorder ms grill_rotated.pdf
2025-07-18 01:23:54,371 - shyam_rag_system - WARNING - No content extracted from Papa\List of Carpantery Material 2.pdf
2025-07-18 01:24:23,357 - shyam_rag_system - WARNING - No content extracted from Papa\Scanned Documents.pdf
2025-07-18 01:24:29,798 - shyam_rag_system - WARNING - No content extracted from Papa\SQ4.pdf
2025-07-18 01:24:42,249 - shyam_rag_system - WARNING - No content extracted from Papa\Documents\Aadhar Card - Anil.pdf
2025-07-18 01:24:42,267 - shyam_rag_system - WARNING - No content extracted from Papa\Documents\Cancelled cheque.pdf
2025-07-18 01:24:42,289 - shyam_rag_system - WARNING - No content extracted from Papa\Documents\GST Certificate.pdf
2025-07-18 01:24:42,306 - shyam_rag_system - WARNING - No content extracted from Papa\Documents\Pancard.pdf
2025-07-18 01:25:45,101 - shyam_rag_system - WARNING - Mammoth failed for Papa\~$ Lakshay.docx, trying python-docx: File is not a zip file
2025-07-18 01:25:45,102 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\~$ Lakshay.docx: Package not found at 'Papa\~$ Lakshay.docx'
2025-07-18 01:25:45,102 - shyam_rag_system - WARNING - No content extracted from Papa\~$ Lakshay.docx
2025-07-18 01:25:45,112 - shyam_rag_system - WARNING - Mammoth failed for Papa\~$. Harkare. .docx, trying python-docx: File is not a zip file
2025-07-18 01:25:45,113 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\~$. Harkare. .docx: Package not found at 'Papa\~$. Harkare. .docx'
2025-07-18 01:25:45,113 - shyam_rag_system - WARNING - No content extracted from Papa\~$. Harkare. .docx
2025-07-18 01:25:45,125 - shyam_rag_system - WARNING - Mammoth failed for Papa\~$. Vineet.docx, trying python-docx: File is not a zip file
2025-07-18 01:25:45,126 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\~$. Vineet.docx: Package not found at 'Papa\~$. Vineet.docx'
2025-07-18 01:25:45,126 - shyam_rag_system - WARNING - No content extracted from Papa\~$. Vineet.docx
2025-07-18 01:25:45,135 - shyam_rag_system - WARNING - Mammoth failed for Papa\~$Doors.docx, trying python-docx: File is not a zip file
2025-07-18 01:25:45,136 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\~$Doors.docx: Package not found at 'Papa\~$Doors.docx'
2025-07-18 01:25:45,136 - shyam_rag_system - WARNING - No content extracted from Papa\~$Doors.docx
2025-07-18 01:25:45,145 - shyam_rag_system - WARNING - Mammoth failed for Papa\~$LIVERY MEMO (1).docx, trying python-docx: File is not a zip file
2025-07-18 01:25:45,146 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\~$LIVERY MEMO (1).docx: Package not found at 'Papa\~$LIVERY MEMO (1).docx'
2025-07-18 01:25:45,147 - shyam_rag_system - WARNING - No content extracted from Papa\~$LIVERY MEMO (1).docx
2025-07-18 01:25:45,159 - shyam_rag_system - WARNING - Mammoth failed for Papa\~$nesh Sangode letter - Copy (2).docx, trying python-docx: File is not a zip file
2025-07-18 01:25:45,160 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\~$nesh Sangode letter - Copy (2).docx: Package not found at 'Papa\~$nesh Sangode letter - Copy (2).docx'
2025-07-18 01:25:45,160 - shyam_rag_system - WARNING - No content extracted from Papa\~$nesh Sangode letter - Copy (2).docx
2025-07-18 01:25:45,171 - shyam_rag_system - WARNING - Mammoth failed for Papa\~$nvoice.docx, trying python-docx: File is not a zip file
2025-07-18 01:25:45,172 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\~$nvoice.docx: Package not found at 'Papa\~$nvoice.docx'
2025-07-18 01:25:45,172 - shyam_rag_system - WARNING - No content extracted from Papa\~$nvoice.docx
2025-07-18 01:25:45,183 - shyam_rag_system - WARNING - Mammoth failed for Papa\~$otation.docx, trying python-docx: File is not a zip file
2025-07-18 01:25:45,184 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\~$otation.docx: Package not found at 'Papa\~$otation.docx'
2025-07-18 01:25:45,184 - shyam_rag_system - WARNING - No content extracted from Papa\~$otation.docx
2025-07-18 01:25:45,195 - shyam_rag_system - WARNING - Mammoth failed for Papa\~$r Ajet.docx, trying python-docx: File is not a zip file
2025-07-18 01:25:45,197 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\~$r Ajet.docx: Package not found at 'Papa\~$r Ajet.docx'
2025-07-18 01:25:45,198 - shyam_rag_system - WARNING - No content extracted from Papa\~$r Ajet.docx
2025-07-18 01:26:05,475 - shyam_rag_system - WARNING - Mammoth failed for Papa\ORDANCE FACTORY\~$LIVERY MEMO ORDANCE.docx, trying python-docx: File is not a zip file
2025-07-18 01:26:05,476 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\ORDANCE FACTORY\~$LIVERY MEMO ORDANCE.docx: Package not found at 'Papa\ORDANCE FACTORY\~$LIVERY MEMO ORDANCE.docx'
2025-07-18 01:26:05,476 - shyam_rag_system - WARNING - No content extracted from Papa\ORDANCE FACTORY\~$LIVERY MEMO ORDANCE.docx
2025-07-18 01:26:05,487 - shyam_rag_system - WARNING - Mammoth failed for Papa\ORDANCE FACTORY\~$LIVERY MEMO ORDANCE_54.docx, trying python-docx: File is not a zip file
2025-07-18 01:26:05,488 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\ORDANCE FACTORY\~$LIVERY MEMO ORDANCE_54.docx: Package not found at 'Papa\ORDANCE FACTORY\~$LIVERY MEMO ORDANCE_54.docx'
2025-07-18 01:26:05,489 - shyam_rag_system - WARNING - No content extracted from Papa\ORDANCE FACTORY\~$LIVERY MEMO ORDANCE_54.docx
2025-07-18 01:26:30,864 - shyam_rag_system - WARNING - Mammoth failed for Papa\Bill 2021\Word\~$voice 53 (2022) (Keti).docx, trying python-docx: File is not a zip file
2025-07-18 01:26:30,864 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\Bill 2021\Word\~$voice 53 (2022) (Keti).docx: Package not found at 'Papa\Bill 2021\Word\~$voice 53 (2022) (Keti).docx'
2025-07-18 01:26:30,865 - shyam_rag_system - WARNING - No content extracted from Papa\Bill 2021\Word\~$voice 53 (2022) (Keti).docx
2025-07-18 01:26:30,876 - shyam_rag_system - WARNING - Mammoth failed for Papa\Bill 2021\Word\~$voice 54 (2022) (SL Realty).docx, trying python-docx: File is not a zip file
2025-07-18 01:26:30,876 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\Bill 2021\Word\~$voice 54 (2022) (SL Realty).docx: Package not found at 'Papa\Bill 2021\Word\~$voice 54 (2022) (SL Realty).docx'
2025-07-18 01:26:30,877 - shyam_rag_system - WARNING - No content extracted from Papa\Bill 2021\Word\~$voice 54 (2022) (SL Realty).docx
2025-07-18 01:26:30,902 - shyam_rag_system - WARNING - Mammoth failed for Papa\Bill 2021\Word\~$voice 56 (2022) (DP Jain).docx, trying python-docx: File is not a zip file
2025-07-18 01:26:30,902 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\Bill 2021\Word\~$voice 56 (2022) (DP Jain).docx: Package not found at 'Papa\Bill 2021\Word\~$voice 56 (2022) (DP Jain).docx'
2025-07-18 01:26:30,903 - shyam_rag_system - WARNING - No content extracted from Papa\Bill 2021\Word\~$voice 56 (2022) (DP Jain).docx
2025-07-18 01:26:30,932 - shyam_rag_system - WARNING - Mammoth failed for Papa\Bill 2021\Word\~$voice 60 (2022) (Ravindra).docx, trying python-docx: File is not a zip file
2025-07-18 01:26:30,933 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\Bill 2021\Word\~$voice 60 (2022) (Ravindra).docx: Package not found at 'Papa\Bill 2021\Word\~$voice 60 (2022) (Ravindra).docx'
2025-07-18 01:26:30,933 - shyam_rag_system - WARNING - No content extracted from Papa\Bill 2021\Word\~$voice 60 (2022) (Ravindra).docx
2025-07-18 01:26:30,944 - shyam_rag_system - WARNING - Mammoth failed for Papa\Bill 2021\Word\~$voice 64 (2022) (JS Trading).docx, trying python-docx: File is not a zip file
2025-07-18 01:26:30,945 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\Bill 2021\Word\~$voice 64 (2022) (JS Trading).docx: Package not found at 'Papa\Bill 2021\Word\~$voice 64 (2022) (JS Trading).docx'
2025-07-18 01:26:30,945 - shyam_rag_system - WARNING - No content extracted from Papa\Bill 2021\Word\~$voice 64 (2022) (JS Trading).docx
2025-07-18 01:26:30,961 - shyam_rag_system - WARNING - Mammoth failed for Papa\Bill 2021\Word\~$voice 65 (2022) (Keti).docx, trying python-docx: File is not a zip file
2025-07-18 01:26:30,961 - shyam_rag_system - ERROR - Both DOCX extraction methods failed for Papa\Bill 2021\Word\~$voice 65 (2022) (Keti).docx: Package not found at 'Papa\Bill 2021\Word\~$voice 65 (2022) (Keti).docx'
2025-07-18 01:26:30,962 - shyam_rag_system - WARNING - No content extracted from Papa\Bill 2021\Word\~$voice 65 (2022) (Keti).docx
2025-07-18 01:26:30,962 - shyam_rag_system - INFO - Generated 3850 chunks from 1415 documents
2025-07-18 01:26:30,963 - shyam_rag_system - INFO - Adding 3850 documents to vector store
2025-07-18 01:27:19,241 - shyam_rag_system - INFO - Vector store now contains 3850 documents
2025-07-18 01:27:19,364 - shyam_rag_system - INFO - Vector store saved to shyam_rag_db
2025-07-18 01:27:19,365 - shyam_rag_system - INFO - RAG database built successfully!
2025-07-18 01:27:19,365 - __main__ - ERROR - Database build failed!
