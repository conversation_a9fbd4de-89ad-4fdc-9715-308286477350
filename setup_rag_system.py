#!/usr/bin/env python3
"""
Shyam Trading Company - RAG System Setup Script
===============================================

This script sets up the advanced RAG system for Shyam Trading Company.
It handles package installation, model downloads, and initial database creation.

Usage:
    python setup_rag_system.py [--force-rebuild] [--source-dir Papa/]

Author: AI Assistant for Shyam Trading Company
Date: 2025
"""

import os
import sys
import subprocess
import argparse
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        sys.exit(1)
    logger.info(f"Python version: {sys.version}")

def install_requirements():
    """Install required packages"""
    logger.info("Installing required packages...")
    
    try:
        # Check if we're in a virtual environment
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            logger.info("Virtual environment detected ✓")
        else:
            logger.warning("Not in a virtual environment. Consider using one.")
        
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_rag.txt"])
        logger.info("Packages installed successfully ✓")
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install packages: {e}")
        return False
    
    return True

def download_spacy_model():
    """Download spaCy English model"""
    logger.info("Downloading spaCy English model...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])
        logger.info("spaCy model downloaded successfully ✓")
    except subprocess.CalledProcessError as e:
        logger.warning(f"Failed to download spaCy model: {e}")
        logger.info("You can download it manually later with: python -m spacy download en_core_web_sm")
        return False
    
    return True

def check_documents_directory(source_dir):
    """Check if documents directory exists and has files"""
    source_path = Path(source_dir)
    
    if not source_path.exists():
        logger.error(f"Source directory '{source_dir}' does not exist")
        return False
    
    # Count PDF and DOCX files
    pdf_files = list(source_path.glob("**/*.pdf"))
    docx_files = list(source_path.glob("**/*.docx"))
    
    total_files = len(pdf_files) + len(docx_files)
    
    if total_files == 0:
        logger.warning(f"No PDF or DOCX files found in '{source_dir}'")
        return False
    
    logger.info(f"Found {len(pdf_files)} PDF files and {len(docx_files)} DOCX files")
    return True

def create_config_file():
    """Create default configuration file"""
    config_content = """# Shyam Trading Company RAG Configuration
documents:
  source_directory: "Papa/"
  supported_formats: [".pdf", ".docx"]
  max_file_size_mb: 50

chunking:
  strategy: "hybrid"  # semantic, fixed, hybrid
  chunk_size: 800
  chunk_overlap: 200
  min_chunk_size: 100

embeddings:
  model_name: "sentence-transformers/all-mpnet-base-v2"
  batch_size: 32
  normalize_embeddings: true

search:
  hybrid_alpha: 0.7  # Weight for semantic vs keyword search
  top_k_initial: 20
  top_k_final: 5
  enable_reranking: true
  reranker_model: "cross-encoder/ms-marco-MiniLM-L-6-v2"

database:
  vector_db: "faiss"  # faiss, chroma
  persist_directory: "shyam_rag_db"
  index_type: "IVF"
  distance_metric: "cosine"

business_rules:
  invoice_keywords: ["invoice", "bill", "payment", "amount", "tax"]
  quotation_keywords: ["quotation", "quote", "estimate", "proposal"]
  account_keywords: ["account", "statement", "balance", "ledger"]
  construction_terms: ["aluminium", "door", "window", "ACP", "glass", "construction"]
"""
    
    with open("shyam_rag_config.yaml", "w") as f:
        f.write(config_content)
    
    logger.info("Configuration file created: shyam_rag_config.yaml")

def test_rag_system(source_dir, force_rebuild):
    """Test the RAG system setup"""
    logger.info("Testing RAG system...")
    
    try:
        # Import the RAG system
        from shyam_rag_system import quick_setup
        
        # Create RAG system
        rag = quick_setup(source_dir, force_rebuild)
        
        # Get stats
        stats = rag.get_document_stats()
        
        logger.info("RAG System Test Results:")
        logger.info(f"  Total documents: {stats['total_documents']}")
        logger.info(f"  Document types: {stats['document_types']}")
        logger.info(f"  Unique clients: {stats['unique_clients']}")
        
        # Test query
        result = rag.query("aluminium door", top_k=3)
        logger.info(f"  Test query returned: {len(result['results'])} results")
        
        logger.info("RAG system test completed successfully ✓")
        return True
        
    except Exception as e:
        logger.error(f"RAG system test failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Setup Shyam Trading RAG System")
    parser.add_argument("--force-rebuild", action="store_true", 
                       help="Force rebuild of the database even if it exists")
    parser.add_argument("--source-dir", default="Papa/", 
                       help="Source directory containing documents (default: Papa/)")
    parser.add_argument("--skip-install", action="store_true",
                       help="Skip package installation")
    parser.add_argument("--skip-test", action="store_true",
                       help="Skip system testing")
    
    args = parser.parse_args()
    
    print("🏗️  Shyam Trading Company - RAG System Setup")
    print("=" * 50)
    
    # Check Python version
    check_python_version()
    
    # Install packages
    if not args.skip_install:
        if not install_requirements():
            logger.error("Package installation failed")
            sys.exit(1)
        
        download_spacy_model()
    
    # Check documents directory
    if not check_documents_directory(args.source_dir):
        logger.error("Document directory check failed")
        sys.exit(1)
    
    # Create configuration file
    create_config_file()
    
    # Test the system
    if not args.skip_test:
        if not test_rag_system(args.source_dir, args.force_rebuild):
            logger.error("System test failed")
            sys.exit(1)
    
    print("\n✅ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Import the system: from shyam_rag_system import ShyamRAGSystem")
    print("2. Create instance: rag = ShyamRAGSystem()")
    print("3. Build database: rag.build_database()")
    print("4. Query documents: result = rag.query('your question')")
    print("5. For n8n integration: n8n_data = rag.export_for_n8n(result)")
    
    print(f"\nConfiguration file: shyam_rag_config.yaml")
    print(f"Database location: shyam_rag_db/")
    print(f"Log file: shyam_rag.log")

if __name__ == "__main__":
    main()
