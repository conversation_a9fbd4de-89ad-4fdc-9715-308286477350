#!/usr/bin/env python3
"""
Quick Start RAG System for Shyam Trading Company
================================================

This script creates a minimal working RAG system using only the packages
that are currently installed, allowing you to start testing immediately
while the full setup completes in the background.

Usage:
    python quick_start_rag.py
"""

import os
import sys
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_available_packages():
    """Check which packages are available for the quick start"""
    available = {}
    
    packages_to_check = {
        'numpy': 'numpy',
        'pandas': 'pandas', 
        'pathlib': 'pathlib',
        'json': 'json',
        'os': 'os',
        'sys': 'sys',
        'logging': 'logging',
        'datetime': 'datetime',
        'hashlib': 'hashlib',
        'pickle': 'pickle'
    }
    
    for name, import_name in packages_to_check.items():
        try:
            __import__(import_name)
            available[name] = True
            logger.info(f"✓ {name} available")
        except ImportError:
            available[name] = False
            logger.warning(f"✗ {name} not available")
    
    return available

def create_minimal_document_processor():
    """Create a basic document processor using only standard library"""
    
    class MinimalDocumentProcessor:
        def __init__(self):
            self.supported_formats = ['.txt', '.py', '.md']  # Start with text files
            
        def process_document(self, file_path):
            """Process a text document and return basic chunks"""
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Simple chunking by paragraphs
                chunks = []
                paragraphs = content.split('\n\n')
                
                for i, paragraph in enumerate(paragraphs):
                    if len(paragraph.strip()) > 50:  # Only keep substantial paragraphs
                        chunk_data = {
                            'content': paragraph.strip(),
                            'metadata': {
                                'file_path': str(file_path),
                                'file_name': Path(file_path).name,
                                'chunk_index': i,
                                'total_chunks': len(paragraphs)
                            }
                        }
                        chunks.append(chunk_data)
                
                return chunks
                
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                return []
        
        def process_directory(self, directory):
            """Process all supported files in a directory"""
            directory = Path(directory)
            all_chunks = []
            
            for file_path in directory.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    logger.info(f"Processing: {file_path}")
                    chunks = self.process_document(file_path)
                    all_chunks.extend(chunks)
            
            return all_chunks
    
    return MinimalDocumentProcessor()

def create_simple_search_system():
    """Create a basic keyword search system"""
    
    class SimpleSearchSystem:
        def __init__(self):
            self.documents = []
            self.index = {}
        
        def add_documents(self, chunks):
            """Add document chunks to the search system"""
            self.documents.extend(chunks)
            
            # Create simple keyword index
            for i, chunk in enumerate(chunks):
                content = chunk['content'].lower()
                words = content.split()
                
                for word in words:
                    # Clean word
                    word = ''.join(c for c in word if c.isalnum())
                    if len(word) > 2:  # Only index words longer than 2 characters
                        if word not in self.index:
                            self.index[word] = []
                        self.index[word].append(len(self.documents) - len(chunks) + i)
        
        def search(self, query, top_k=5):
            """Search for documents matching the query"""
            query_words = [word.lower().strip('.,!?;:') for word in query.split()]
            query_words = [w for w in query_words if len(w) > 2]
            
            if not query_words:
                return []
            
            # Score documents
            doc_scores = {}
            
            for word in query_words:
                if word in self.index:
                    for doc_idx in self.index[word]:
                        if doc_idx not in doc_scores:
                            doc_scores[doc_idx] = 0
                        doc_scores[doc_idx] += 1
            
            # Sort by score and return top results
            sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)
            
            results = []
            for doc_idx, score in sorted_docs[:top_k]:
                if doc_idx < len(self.documents):
                    result = {
                        'content': self.documents[doc_idx]['content'],
                        'score': score,
                        'metadata': self.documents[doc_idx]['metadata']
                    }
                    results.append(result)
            
            return results
        
        def get_stats(self):
            """Get basic statistics"""
            return {
                'total_documents': len(self.documents),
                'total_words_indexed': len(self.index),
                'document_types': {}
            }
    
    return SimpleSearchSystem()

def create_quick_rag_system():
    """Create a minimal RAG system for immediate testing"""
    
    class QuickRAGSystem:
        def __init__(self):
            self.processor = create_minimal_document_processor()
            self.search_system = create_simple_search_system()
            self.is_built = False
        
        def build_database(self, source_directory="Papa/"):
            """Build a simple database from documents"""
            logger.info(f"Building quick RAG database from {source_directory}")
            
            # Check if directory exists
            if not Path(source_directory).exists():
                logger.error(f"Directory {source_directory} not found")
                return False
            
            # Process documents
            chunks = self.processor.process_directory(source_directory)
            
            if not chunks:
                logger.warning("No documents processed. Trying with current directory files...")
                # Try processing Python and text files in current directory
                chunks = self.processor.process_directory(".")
            
            if chunks:
                logger.info(f"Processed {len(chunks)} chunks")
                self.search_system.add_documents(chunks)
                self.is_built = True
                logger.info("Quick RAG database built successfully!")
                return True
            else:
                logger.error("No documents could be processed")
                return False
        
        def query(self, question, top_k=5):
            """Query the RAG system"""
            if not self.is_built:
                logger.error("Database not built. Call build_database() first.")
                return {'results': [], 'error': 'Database not built'}
            
            results = self.search_system.search(question, top_k)
            
            return {
                'query': question,
                'results': results,
                'total_results': len(results)
            }
        
        def get_stats(self):
            """Get database statistics"""
            if not self.is_built:
                return {'error': 'Database not built'}
            
            return self.search_system.get_stats()
    
    return QuickRAGSystem()

def main():
    print("🚀 Shyam Trading Company - Quick Start RAG System")
    print("=" * 60)
    print("This is a minimal RAG system for immediate testing while")
    print("the full system setup completes in the background.")
    print("=" * 60)
    
    # Check available packages
    logger.info("Checking available packages...")
    available = check_available_packages()
    
    if not available.get('numpy') or not available.get('pandas'):
        logger.warning("Core packages not yet installed. The full setup is still running.")
        logger.info("You can still test with basic text processing.")
    
    # Create and test the quick RAG system
    try:
        rag = create_quick_rag_system()
        
        # Build database
        success = rag.build_database("Papa/")
        
        if success:
            # Get statistics
            stats = rag.get_stats()
            logger.info(f"Database stats: {stats}")
            
            # Test queries
            test_queries = [
                "aluminium door",
                "construction",
                "quotation",
                "invoice",
                "trading"
            ]
            
            print("\n🔍 Testing Sample Queries:")
            print("-" * 40)
            
            for query in test_queries:
                result = rag.query(query, top_k=3)
                print(f"\nQuery: '{query}'")
                print(f"Results: {result['total_results']}")
                
                for i, res in enumerate(result['results'][:2]):  # Show top 2
                    content_preview = res['content'][:100] + "..." if len(res['content']) > 100 else res['content']
                    print(f"  {i+1}. Score: {res['score']} | {content_preview}")
            
            print("\n✅ Quick RAG system is working!")
            print("\n📋 Next Steps:")
            print("1. Wait for full setup to complete (check shyam_rag_setup.log)")
            print("2. Once complete, you'll have advanced features like:")
            print("   - PDF and DOCX processing")
            print("   - Semantic search with embeddings")
            print("   - Hybrid search and reranking")
            print("   - Business document classification")
            print("   - n8n API integration")
            
        else:
            print("❌ Could not build database. Check if Papa/ directory exists with documents.")
            
    except Exception as e:
        logger.error(f"Error creating quick RAG system: {e}")
        print("❌ Quick start failed. Wait for full setup to complete.")

if __name__ == "__main__":
    main()
