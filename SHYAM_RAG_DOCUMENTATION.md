# Shyam Trading Company - Advanced RAG System Documentation

## 🏗️ Overview

This is a production-ready RAG (Retrieval-Augmented Generation) system specifically designed for Shyam Trading Company's business documents. It implements the latest 2025 best practices including hybrid search, advanced chunking, reranking, and business-specific document processing.

## 🚀 Key Features

### Advanced RAG Architecture (2025 Best Practices)
- **Hybrid Search**: Combines semantic (vector) and keyword (BM25) search
- **Reranking**: Uses cross-encoder models for improved accuracy
- **Advanced Chunking**: Semantic, fixed, and hybrid chunking strategies
- **Metadata Filtering**: Filter by document type, client, date, etc.

### Business Document Optimization
- **Format-Specific Parsing**: Optimized for PDF and DOCX files
- **Document Classification**: Auto-classifies invoices, quotations, account statements
- **Entity Extraction**: Extracts clients, amounts, dates, phone numbers, emails
- **Shyam Trading Conventions**: Follows your specific naming patterns

### Integration Ready
- **n8n Compatible**: Direct export format for n8n workflows
- **REST API**: FastAPI-based API for external integrations
- **Fine-tuned Model Ready**: Designed to work with your fine-tuned AI model

## 📁 File Structure

```
shyam-trading/
├── shyam_rag_system.py          # Main RAG system implementation
├── shyam_rag_api.py             # FastAPI REST API interface
├── setup_rag_system.py         # Setup and installation script
├── requirements_rag.txt        # Python dependencies
├── shyam_rag_config.yaml       # Configuration file (auto-generated)
├── shyam_rag_db/               # Vector database storage (auto-created)
└── Papa/                       # Your documents directory
```

## 🛠️ Installation & Setup

### 1. Quick Setup (Recommended)

```bash
# Run the setup script
python setup_rag_system.py

# Or with custom options
python setup_rag_system.py --source-dir Papa/ --force-rebuild
```

### 2. Manual Setup

```bash
# Install dependencies
pip install -r requirements_rag.txt

# Download spaCy model
python -m spacy download en_core_web_sm

# Run the system
python shyam_rag_system.py
```

### 3. Virtual Environment (Recommended)

```bash
# Create virtual environment
python -m venv shyam_rag_env
source shyam_rag_env/bin/activate  # On Windows: shyam_rag_env\Scripts\activate

# Install and setup
pip install -r requirements_rag.txt
python setup_rag_system.py
```

## 💻 Usage Examples

### Basic Python Usage

```python
from shyam_rag_system import ShyamRAGSystem

# Initialize system
rag = ShyamRAGSystem()

# Build database (first time only)
rag.build_database("Papa/")

# Query documents
result = rag.query("aluminium doors and windows quotation")
print(f"Found {len(result['results'])} relevant documents")

# Filter by document type
invoices = rag.query("payment amount", document_type="invoice")

# Filter by client
client_docs = rag.query("construction work", client_name="Mr. Anil")

# Get database statistics
stats = rag.get_document_stats()
print(f"Total documents: {stats['total_documents']}")
```

### API Usage

```bash
# Start the API server
python shyam_rag_api.py

# The API will be available at http://localhost:8000
# Documentation at http://localhost:8000/docs
```

#### API Endpoints

```bash
# Health check
curl http://localhost:8000/health

# Query documents
curl -X POST http://localhost:8000/query \
  -H "Content-Type: application/json" \
  -d '{"question": "aluminium door quotation", "top_k": 5}'

# Get database statistics
curl http://localhost:8000/stats

# Rebuild database
curl -X POST http://localhost:8000/rebuild \
  -H "Content-Type: application/json" \
  -d '{"source_directory": "Papa/", "force_rebuild": false}'
```

### n8n Integration

```javascript
// n8n HTTP Request Node configuration
{
  "method": "POST",
  "url": "http://localhost:8000/export/n8n",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "question": "{{$json.user_query}}",
    "document_type": "{{$json.doc_type}}",
    "top_k": 5
  }
}
```

## ⚙️ Configuration

The system uses `shyam_rag_config.yaml` for configuration:

```yaml
documents:
  source_directory: "Papa/"
  supported_formats: [".pdf", ".docx"]
  max_file_size_mb: 50

chunking:
  strategy: "hybrid"  # semantic, fixed, hybrid
  chunk_size: 800
  chunk_overlap: 200

embeddings:
  model_name: "sentence-transformers/all-mpnet-base-v2"
  batch_size: 32

search:
  hybrid_alpha: 0.7  # Weight for semantic vs keyword
  top_k_initial: 20
  top_k_final: 5
  enable_reranking: true

business_rules:
  invoice_keywords: ["invoice", "bill", "payment"]
  quotation_keywords: ["quotation", "quote", "estimate"]
  account_keywords: ["account", "statement", "balance"]
```

## 🎯 Document Classification

The system automatically classifies documents based on:

### Filename Patterns (Shyam Trading Conventions)
- **Invoices**: Files starting with "invoice"
- **Account Statements**: Files starting with "Account"
- **Quotations**: Files starting with "Mr.", "Dr.", "Ms." or company names

### Content Analysis
- Keyword matching for document types
- Entity extraction (clients, amounts, dates)
- Business-specific term recognition

## 🔍 Search Capabilities

### Hybrid Search
- **Semantic Search**: Understanding meaning and context
- **Keyword Search**: Exact term matching using BM25
- **Combined Scoring**: Weighted combination of both approaches

### Advanced Features
- **Reranking**: Cross-encoder models for improved relevance
- **Metadata Filtering**: Filter by type, client, date, etc.
- **Business Context**: Optimized for construction/architectural terms

## 📊 Performance & Scalability

### Optimizations
- **Batch Processing**: Efficient document processing
- **FAISS Indexing**: Fast vector similarity search
- **Caching**: Persistent database storage
- **Memory Efficient**: Streaming and batch processing

### Scalability
- **Document Capacity**: Handles thousands of documents
- **Query Speed**: Sub-second response times
- **Incremental Updates**: Add new documents without full rebuild

## 🔧 Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Install missing packages
   pip install -r requirements_rag.txt
   python -m spacy download en_core_web_sm
   ```

2. **Memory Issues**
   ```python
   # Reduce batch size in config
   embeddings:
     batch_size: 16  # Reduce from 32
   ```

3. **Slow Performance**
   ```python
   # Disable reranking for faster queries
   search:
     enable_reranking: false
   ```

4. **No Documents Found**
   ```bash
   # Check document directory
   python -c "from pathlib import Path; print(list(Path('Papa/').glob('**/*.pdf')))"
   ```

## 🚀 Advanced Usage

### Custom Document Processing
```python
# Custom document processor
processor = AdvancedDocumentProcessor(config)
chunks = processor.process_document("path/to/document.pdf")
```

### Custom Search Filters
```python
# Advanced filtering
result = rag.query(
    "construction materials",
    document_type="quotation",
    client_name="Mr. Anil",
    top_k=10
)
```

### Integration with Fine-tuned Model
```python
# Use RAG results with your fine-tuned model
rag_result = rag.query("aluminium door specifications")
context = "\n".join([r['content'] for r in rag_result['results']])

# Pass to your fine-tuned model
response = your_finetuned_model.generate(
    prompt=f"Context: {context}\n\nQuestion: {question}",
    max_length=500
)
```

## 📈 Monitoring & Maintenance

### Database Statistics
```python
stats = rag.get_document_stats()
print(f"Documents: {stats['total_documents']}")
print(f"Types: {stats['document_types']}")
print(f"Clients: {stats['unique_clients']}")
```

### Log Monitoring
- Check `shyam_rag.log` for system logs
- Monitor API logs for query patterns
- Track processing errors and warnings

### Regular Maintenance
- Rebuild database when adding new documents
- Update embeddings model periodically
- Monitor query performance and adjust configuration

## 🤝 Support & Development

For issues or enhancements:
1. Check the logs in `shyam_rag.log`
2. Verify configuration in `shyam_rag_config.yaml`
3. Test with the setup script: `python setup_rag_system.py --skip-install`

---

**Built for Shyam Trading Company with ❤️ using 2025 RAG best practices**
