# Shyam Trading Company - RAG System Requirements
# Advanced RAG system with hybrid search and business document processing

# Core ML and NLP
torch>=2.0.0
transformers>=4.30.0
sentence-transformers>=2.2.2
datasets>=2.14.0

# Document processing
PyPDF2>=3.0.1
PyMuPDF>=1.23.0  # fitz for better PDF parsing
python-docx>=0.8.11
mammoth>=1.6.0  # Better docx to text conversion

# Text processing and NLP
spacy>=3.6.0
langchain>=0.1.0
langchain-community>=0.0.20

# Vector databases and search
faiss-cpu>=1.7.4  # Use faiss-gpu if you have CUDA
chromadb>=0.4.0
rank-bm25>=0.2.2

# Reranking
sentence-transformers[reranking]>=2.2.2

# Data processing
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Utilities
tqdm>=4.65.0
pyyaml>=6.0
python-dotenv>=1.0.0

# Visualization (optional)
matplotlib>=3.7.0
seaborn>=0.12.0

# Development and testing
pytest>=7.4.0
jupyter>=1.0.0

# Optional: GPU acceleration (uncomment if you have CUDA)
# faiss-gpu>=1.7.4
# torch-audio>=2.0.0

# Note: After installation, run:
# python -m spacy download en_core_web_sm
