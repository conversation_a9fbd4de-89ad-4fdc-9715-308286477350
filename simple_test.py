import os
import sys

print("Current working directory:", os.getcwd())
print("Python version:", sys.version)

# Check if Papa folder exists
if os.path.exists("Papa"):
    print("✅ Papa folder exists")
    
    # List some files
    files = os.listdir("Papa")[:10]  # First 10 files
    print("First 10 files in Papa:")
    for f in files:
        print(f"  - {f}")
        
    # Check specific files
    test_files = [
        "Papa/Mr. Aditya.pdf",
        "Papa/Mr. Aditya.docx", 
        "Papa/Invoice 25-26/Invoice 1 - 4-5.pdf"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Not found: {file_path}")
            
    # Try to import required libraries
    try:
        import pdfplumber
        print("✅ pdfplumber imported successfully")
    except ImportError as e:
        print(f"❌ pdfplumber import failed: {e}")
        
    try:
        from docx import Document
        print("✅ docx imported successfully")
    except ImportError as e:
        print(f"❌ docx import failed: {e}")
        
else:
    print("❌ Papa folder not found")
    print("Available items in current directory:")
    for item in os.listdir("."):
        print(f"  - {item}")
