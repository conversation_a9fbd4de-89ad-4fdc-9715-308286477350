"""
Shyam Trading Company - Advanced RAG System (2025)
==================================================

A production-ready RAG system implementing latest 2025 best practices:
- Hybrid search (semantic + keyword)
- Advanced chunking strategies
- Metadata extraction and filtering
- Reranking for improved accuracy
- Business document optimization
- Integration-ready for n8n workflows

Author: AI Assistant for Shyam Trading Company
Date: 2025
"""

import os
import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import hashlib
import pickle

# Core libraries
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict

# Document processing
import PyPDF2
import fitz  # PyMuPDF for better PDF parsing
from docx import Document
import mammoth  # Better docx to text conversion

# Text processing and embeddings
from sentence_transformers import SentenceTransformer
import spacy
from transformers import AutoTokenizer, AutoModel
import torch

# Vector databases and search
import faiss
from rank_bm25 import BM25Okapi
import chromadb
from chromadb.config import Settings

# Advanced text processing
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import DirectoryLoader
from langchain.schema import Document as LangchainDocument

# Reranking
from sentence_transformers import CrossEncoder

# Configuration and utilities
import yaml
from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('shyam_rag.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DocumentMetadata:
    """Enhanced metadata structure for business documents"""
    file_path: str
    file_name: str
    document_type: str  # invoice, account_statement, quotation, other
    client_name: Optional[str] = None
    document_date: Optional[str] = None
    amount: Optional[float] = None
    project_type: Optional[str] = None
    file_hash: Optional[str] = None
    processing_date: Optional[str] = None
    chunk_index: Optional[int] = None
    total_chunks: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class ShyamRAGConfig:
    """Configuration class for RAG system"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self._load_config(config_path)
        
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """Load configuration from file or use defaults"""
        default_config = {
            "documents": {
                "source_directory": "Papa/",
                "supported_formats": [".pdf", ".docx"],
                "max_file_size_mb": 50
            },
            "chunking": {
                "strategy": "hybrid",  # semantic, fixed, hybrid
                "chunk_size": 800,
                "chunk_overlap": 200,
                "min_chunk_size": 100
            },
            "embeddings": {
                "model_name": "sentence-transformers/all-mpnet-base-v2",
                "batch_size": 32,
                "normalize_embeddings": True
            },
            "search": {
                "hybrid_alpha": 0.7,  # Weight for semantic vs keyword search
                "top_k_initial": 20,
                "top_k_final": 5,
                "enable_reranking": True,
                "reranker_model": "cross-encoder/ms-marco-MiniLM-L-6-v2"
            },
            "database": {
                "vector_db": "faiss",  # faiss, chroma
                "persist_directory": "shyam_rag_db",
                "index_type": "IVF",
                "distance_metric": "cosine"
            },
            "business_rules": {
                "invoice_keywords": ["invoice", "bill", "payment", "amount", "tax"],
                "quotation_keywords": ["quotation", "quote", "estimate", "proposal"],
                "account_keywords": ["account", "statement", "balance", "ledger"],
                "construction_terms": ["aluminium", "door", "window", "ACP", "glass", "construction"]
            }
        }
        
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r') as f:
                user_config = yaml.safe_load(f)
                default_config.update(user_config)
                
        return default_config
    
    def get(self, key_path: str, default=None):
        """Get nested configuration value using dot notation"""
        keys = key_path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value

class AdvancedDocumentProcessor:
    """Advanced document processor with business-specific parsing"""
    
    def __init__(self, config: ShyamRAGConfig):
        self.config = config
        self.nlp = None
        self._load_nlp_model()
        
    def _load_nlp_model(self):
        """Load spaCy model for NER and text processing"""
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
            self.nlp = None
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate MD5 hash of file for change detection"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _extract_pdf_content(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Extract text and metadata from PDF using multiple methods"""
        text = ""
        metadata = {}
        
        try:
            # Method 1: PyMuPDF (better for complex layouts)
            doc = fitz.open(file_path)
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text += page.get_text()
            doc.close()
            
            # Extract basic metadata
            doc = fitz.open(file_path)
            metadata.update(doc.metadata)
            doc.close()
            
        except Exception as e:
            logger.warning(f"PyMuPDF failed for {file_path}, trying PyPDF2: {e}")
            
            # Fallback: PyPDF2
            try:
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        text += page.extract_text()
                    
                    if pdf_reader.metadata:
                        metadata.update(pdf_reader.metadata)
                        
            except Exception as e2:
                logger.error(f"Both PDF extraction methods failed for {file_path}: {e2}")
                
        return text, metadata
    
    def _extract_docx_content(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Extract text and metadata from DOCX"""
        text = ""
        metadata = {}
        
        try:
            # Method 1: mammoth (better formatting preservation)
            with open(file_path, "rb") as docx_file:
                result = mammoth.extract_raw_text(docx_file)
                text = result.value
                
        except Exception as e:
            logger.warning(f"Mammoth failed for {file_path}, trying python-docx: {e}")
            
            # Fallback: python-docx
            try:
                doc = Document(file_path)
                text = "\n".join([paragraph.text for paragraph in doc.paragraphs])
                
                # Extract metadata
                core_props = doc.core_properties
                metadata = {
                    'author': core_props.author,
                    'created': core_props.created,
                    'modified': core_props.modified,
                    'title': core_props.title,
                    'subject': core_props.subject
                }
                
            except Exception as e2:
                logger.error(f"Both DOCX extraction methods failed for {file_path}: {e2}")
                
        return text, metadata

    def _classify_document_type(self, file_name: str, content: str) -> str:
        """Classify document type based on filename and content"""
        file_name_lower = file_name.lower()
        content_lower = content.lower()

        # Rule-based classification based on Shyam Trading naming conventions
        if file_name_lower.startswith('invoice'):
            return 'invoice'
        elif file_name_lower.startswith('account'):
            return 'account_statement'
        elif any(prefix in file_name_lower for prefix in ['mr.', 'dr.', 'ms.']):
            return 'quotation'
        elif any(company_indicator in file_name_lower for company_indicator in
                ['ltd', 'pvt', 'company', 'construction', 'engineering']):
            return 'quotation'

        # Content-based classification
        invoice_score = sum(1 for keyword in self.config.get('business_rules.invoice_keywords', [])
                           if keyword in content_lower)
        quotation_score = sum(1 for keyword in self.config.get('business_rules.quotation_keywords', [])
                             if keyword in content_lower)
        account_score = sum(1 for keyword in self.config.get('business_rules.account_keywords', [])
                           if keyword in content_lower)

        scores = {
            'invoice': invoice_score,
            'quotation': quotation_score,
            'account_statement': account_score
        }

        max_score_type = max(scores, key=scores.get)
        return max_score_type if scores[max_score_type] > 0 else 'other'

    def _extract_business_entities(self, content: str, doc_type: str) -> Dict[str, Any]:
        """Extract business-specific entities using NLP and regex"""
        entities = {}

        if not self.nlp:
            return entities

        doc = self.nlp(content)

        # Extract named entities
        for ent in doc.ents:
            if ent.label_ == "PERSON":
                entities.setdefault('persons', []).append(ent.text)
            elif ent.label_ == "ORG":
                entities.setdefault('organizations', []).append(ent.text)
            elif ent.label_ == "MONEY":
                entities.setdefault('amounts', []).append(ent.text)
            elif ent.label_ == "DATE":
                entities.setdefault('dates', []).append(ent.text)

        # Business-specific extraction patterns
        import re

        # Extract amounts (Indian currency format)
        amount_patterns = [
            r'₹\s*[\d,]+\.?\d*',
            r'Rs\.?\s*[\d,]+\.?\d*',
            r'INR\s*[\d,]+\.?\d*'
        ]

        for pattern in amount_patterns:
            amounts = re.findall(pattern, content, re.IGNORECASE)
            if amounts:
                entities.setdefault('currency_amounts', []).extend(amounts)

        # Extract phone numbers
        phone_pattern = r'[\+]?[1-9]?[0-9]{7,15}'
        phones = re.findall(phone_pattern, content)
        if phones:
            entities['phone_numbers'] = phones

        # Extract email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, content)
        if emails:
            entities['emails'] = emails

        return entities

    def _extract_client_name(self, filename: str, entities: Dict[str, Any]) -> Optional[str]:
        """Extract client name from filename or entities"""
        # From filename (for quotations starting with Mr./Dr./Ms.)
        if filename.lower().startswith(('mr.', 'dr.', 'ms.')):
            # Extract name after prefix
            name_part = filename[3:].split('.')[0].strip()
            return name_part

        # From extracted persons (take first person mentioned)
        if 'persons' in entities and entities['persons']:
            return entities['persons'][0]

        # From organizations (for company quotations)
        if 'organizations' in entities and entities['organizations']:
            return entities['organizations'][0]

        return None

    def _chunk_document(self, content: str, metadata: Dict[str, Any]) -> List[LangchainDocument]:
        """Advanced chunking with multiple strategies"""
        strategy = self.config.get('chunking.strategy', 'hybrid')
        chunk_size = self.config.get('chunking.chunk_size', 800)
        chunk_overlap = self.config.get('chunking.chunk_overlap', 200)
        min_chunk_size = self.config.get('chunking.min_chunk_size', 100)

        chunks = []

        if strategy == 'semantic':
            chunks = self._semantic_chunking(content, chunk_size, chunk_overlap)
        elif strategy == 'fixed':
            chunks = self._fixed_chunking(content, chunk_size, chunk_overlap)
        else:  # hybrid
            chunks = self._hybrid_chunking(content, chunk_size, chunk_overlap)

        # Filter out chunks that are too small
        chunks = [chunk for chunk in chunks if len(chunk.strip()) >= min_chunk_size]

        # Create LangchainDocument objects with metadata
        documents = []
        for i, chunk in enumerate(chunks):
            chunk_metadata = metadata.copy()
            chunk_metadata['chunk_index'] = i
            chunk_metadata['total_chunks'] = len(chunks)

            doc = LangchainDocument(
                page_content=chunk,
                metadata=chunk_metadata
            )
            documents.append(doc)

        return documents

    def _semantic_chunking(self, content: str, chunk_size: int, overlap: int) -> List[str]:
        """Semantic chunking based on sentence boundaries and topics"""
        # Use sentence boundaries for more coherent chunks
        sentences = content.split('. ')
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            if len(current_chunk) + len(sentence) < chunk_size:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    def _fixed_chunking(self, content: str, chunk_size: int, overlap: int) -> List[str]:
        """Fixed-size chunking with overlap"""
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=overlap,
            separators=["\n\n", "\n", ". ", " ", ""]
        )

        chunks = text_splitter.split_text(content)
        return chunks

    def _hybrid_chunking(self, content: str, chunk_size: int, overlap: int) -> List[str]:
        """Hybrid approach combining semantic and fixed chunking"""
        # First, try semantic chunking
        semantic_chunks = self._semantic_chunking(content, chunk_size, overlap)

        # If chunks are too large, apply fixed chunking
        final_chunks = []
        for chunk in semantic_chunks:
            if len(chunk) > chunk_size * 1.5:  # 50% tolerance
                sub_chunks = self._fixed_chunking(chunk, chunk_size, overlap)
                final_chunks.extend(sub_chunks)
            else:
                final_chunks.append(chunk)

        return final_chunks

    def process_document(self, file_path: str) -> List[LangchainDocument]:
        """Process a single document and return chunks with metadata"""
        file_path = Path(file_path)

        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return []

        # Check file size
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        max_size = self.config.get('documents.max_file_size_mb', 50)
        if file_size_mb > max_size:
            logger.warning(f"File {file_path} ({file_size_mb:.1f}MB) exceeds max size ({max_size}MB)")
            return []

        # Extract content based on file type
        file_extension = file_path.suffix.lower()
        if file_extension == '.pdf':
            content, file_metadata = self._extract_pdf_content(str(file_path))
        elif file_extension == '.docx':
            content, file_metadata = self._extract_docx_content(str(file_path))
        else:
            logger.warning(f"Unsupported file type: {file_extension}")
            return []

        if not content.strip():
            logger.warning(f"No content extracted from {file_path}")
            return []

        # Classify document type
        doc_type = self._classify_document_type(file_path.name, content)

        # Extract business entities
        entities = self._extract_business_entities(content, doc_type)

        # Create base metadata
        base_metadata = DocumentMetadata(
            file_path=str(file_path),
            file_name=file_path.name,
            document_type=doc_type,
            client_name=self._extract_client_name(file_path.name, entities),
            file_hash=self._calculate_file_hash(str(file_path)),
            processing_date=datetime.now().isoformat()
        )

        # Add extracted entities to metadata
        metadata_dict = base_metadata.to_dict()
        metadata_dict.update(entities)
        metadata_dict.update(file_metadata)

        # Chunk the document
        chunks = self._chunk_document(content, metadata_dict)

        return chunks


class HybridVectorStore:
    """Advanced vector store with hybrid search capabilities"""

    def __init__(self, config: ShyamRAGConfig):
        self.config = config
        self.embedding_model = None
        self.reranker = None
        self.faiss_index = None
        self.documents = []
        self.bm25 = None
        self.document_embeddings = None

        self._initialize_models()

    def _initialize_models(self):
        """Initialize embedding and reranking models"""
        model_name = self.config.get('embeddings.model_name')
        logger.info(f"Loading embedding model: {model_name}")

        self.embedding_model = SentenceTransformer(model_name)

        # Initialize reranker if enabled
        if self.config.get('search.enable_reranking'):
            reranker_model = self.config.get('search.reranker_model')
            logger.info(f"Loading reranker model: {reranker_model}")
            self.reranker = CrossEncoder(reranker_model)

    def add_documents(self, documents: List[LangchainDocument]):
        """Add documents to the vector store"""
        if not documents:
            return

        logger.info(f"Adding {len(documents)} documents to vector store")

        # Extract text content
        texts = [doc.page_content for doc in documents]

        # Generate embeddings
        batch_size = self.config.get('embeddings.batch_size', 32)
        embeddings = []

        for i in tqdm(range(0, len(texts), batch_size), desc="Generating embeddings"):
            batch_texts = texts[i:i + batch_size]
            batch_embeddings = self.embedding_model.encode(
                batch_texts,
                normalize_embeddings=self.config.get('embeddings.normalize_embeddings', True),
                show_progress_bar=False
            )
            embeddings.extend(batch_embeddings)

        embeddings = np.array(embeddings)

        # Initialize or update FAISS index
        if self.faiss_index is None:
            dimension = embeddings.shape[1]
            self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity

        self.faiss_index.add(embeddings.astype('float32'))

        # Store documents and embeddings
        self.documents.extend(documents)
        if self.document_embeddings is None:
            self.document_embeddings = embeddings
        else:
            self.document_embeddings = np.vstack([self.document_embeddings, embeddings])

        # Initialize BM25 for keyword search
        tokenized_texts = [text.lower().split() for text in texts]
        if self.bm25 is None:
            self.bm25 = BM25Okapi(tokenized_texts)
        else:
            # Rebuild BM25 with all documents
            all_texts = [doc.page_content.lower().split() for doc in self.documents]
            self.bm25 = BM25Okapi(all_texts)

        logger.info(f"Vector store now contains {len(self.documents)} documents")

    def similarity_search(self, query: str, k: int = 5, filter_metadata: Optional[Dict[str, Any]] = None) -> List[Tuple[LangchainDocument, float]]:
        """Perform hybrid search combining semantic and keyword search"""
        if not self.documents:
            return []

        # Get initial candidates
        top_k_initial = self.config.get('search.top_k_initial', 20)
        semantic_results = self._semantic_search(query, top_k_initial)
        keyword_results = self._keyword_search(query, top_k_initial)

        # Combine results using hybrid scoring
        hybrid_results = self._combine_search_results(
            semantic_results, keyword_results, query
        )

        # Apply metadata filtering
        if filter_metadata:
            hybrid_results = self._filter_by_metadata(hybrid_results, filter_metadata)

        # Rerank if enabled
        if self.reranker and len(hybrid_results) > k:
            hybrid_results = self._rerank_results(query, hybrid_results)

        # Return top k results
        return hybrid_results[:k]

    def _semantic_search(self, query: str, k: int) -> List[Tuple[LangchainDocument, float]]:
        """Perform semantic search using embeddings"""
        query_embedding = self.embedding_model.encode([query], normalize_embeddings=True)

        # Search in FAISS index
        scores, indices = self.faiss_index.search(query_embedding.astype('float32'), k)

        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx < len(self.documents):
                results.append((self.documents[idx], float(score)))

        return results

    def _keyword_search(self, query: str, k: int) -> List[Tuple[LangchainDocument, float]]:
        """Perform keyword search using BM25"""
        query_tokens = query.lower().split()
        scores = self.bm25.get_scores(query_tokens)

        # Get top k indices
        top_indices = np.argsort(scores)[::-1][:k]

        results = []
        for idx in top_indices:
            if idx < len(self.documents):
                results.append((self.documents[idx], float(scores[idx])))

        return results

    def _combine_search_results(self, semantic_results: List[Tuple[LangchainDocument, float]],
                               keyword_results: List[Tuple[LangchainDocument, float]],
                               query: str) -> List[Tuple[LangchainDocument, float]]:
        """Combine semantic and keyword search results with hybrid scoring"""
        alpha = self.config.get('search.hybrid_alpha', 0.7)  # Weight for semantic search

        # Create document to score mapping
        doc_scores = {}

        # Add semantic scores
        for doc, score in semantic_results:
            doc_id = id(doc)
            doc_scores[doc_id] = {
                'document': doc,
                'semantic_score': score,
                'keyword_score': 0.0
            }

        # Add keyword scores
        for doc, score in keyword_results:
            doc_id = id(doc)
            if doc_id in doc_scores:
                doc_scores[doc_id]['keyword_score'] = score
            else:
                doc_scores[doc_id] = {
                    'document': doc,
                    'semantic_score': 0.0,
                    'keyword_score': score
                }

        # Calculate hybrid scores
        hybrid_results = []
        for doc_info in doc_scores.values():
            # Normalize scores (simple min-max normalization)
            semantic_norm = doc_info['semantic_score']
            keyword_norm = doc_info['keyword_score']

            # Combine scores
            hybrid_score = alpha * semantic_norm + (1 - alpha) * keyword_norm

            hybrid_results.append((doc_info['document'], hybrid_score))

        # Sort by hybrid score
        hybrid_results.sort(key=lambda x: x[1], reverse=True)

        return hybrid_results

    def _filter_by_metadata(self, results: List[Tuple[LangchainDocument, float]],
                           filter_metadata: Dict[str, Any]) -> List[Tuple[LangchainDocument, float]]:
        """Filter results based on metadata criteria"""
        filtered_results = []

        for doc, score in results:
            match = True
            for key, value in filter_metadata.items():
                if key not in doc.metadata or doc.metadata[key] != value:
                    match = False
                    break

            if match:
                filtered_results.append((doc, score))

        return filtered_results

    def _rerank_results(self, query: str, results: List[Tuple[LangchainDocument, float]]) -> List[Tuple[LangchainDocument, float]]:
        """Rerank results using cross-encoder model"""
        if not self.reranker or len(results) <= 1:
            return results

        # Prepare query-document pairs
        pairs = [(query, doc.page_content) for doc, _ in results]

        # Get reranking scores
        rerank_scores = self.reranker.predict(pairs)

        # Combine with original scores (weighted average)
        reranked_results = []
        for i, (doc, original_score) in enumerate(results):
            # Weight: 70% reranker, 30% original
            combined_score = 0.7 * rerank_scores[i] + 0.3 * original_score
            reranked_results.append((doc, combined_score))

        # Sort by combined score
        reranked_results.sort(key=lambda x: x[1], reverse=True)

        return reranked_results

    def save_index(self, persist_directory: str):
        """Save the vector store to disk"""
        os.makedirs(persist_directory, exist_ok=True)

        # Save FAISS index
        if self.faiss_index:
            faiss.write_index(self.faiss_index, os.path.join(persist_directory, "faiss_index.bin"))

        # Save documents and metadata
        with open(os.path.join(persist_directory, "documents.pkl"), "wb") as f:
            pickle.dump(self.documents, f)

        # Save embeddings
        if self.document_embeddings is not None:
            np.save(os.path.join(persist_directory, "embeddings.npy"), self.document_embeddings)

        # Save BM25 index
        if self.bm25:
            with open(os.path.join(persist_directory, "bm25.pkl"), "wb") as f:
                pickle.dump(self.bm25, f)

        logger.info(f"Vector store saved to {persist_directory}")

    def load_index(self, persist_directory: str):
        """Load the vector store from disk"""
        if not os.path.exists(persist_directory):
            logger.warning(f"Persist directory {persist_directory} does not exist")
            return

        # Load FAISS index
        faiss_path = os.path.join(persist_directory, "faiss_index.bin")
        if os.path.exists(faiss_path):
            self.faiss_index = faiss.read_index(faiss_path)

        # Load documents
        docs_path = os.path.join(persist_directory, "documents.pkl")
        if os.path.exists(docs_path):
            with open(docs_path, "rb") as f:
                self.documents = pickle.load(f)

        # Load embeddings
        embeddings_path = os.path.join(persist_directory, "embeddings.npy")
        if os.path.exists(embeddings_path):
            self.document_embeddings = np.load(embeddings_path)

        # Load BM25 index
        bm25_path = os.path.join(persist_directory, "bm25.pkl")
        if os.path.exists(bm25_path):
            with open(bm25_path, "rb") as f:
                self.bm25 = pickle.load(f)

        logger.info(f"Vector store loaded from {persist_directory}")


class ShyamRAGSystem:
    """Main RAG system orchestrating all components"""

    def __init__(self, config_path: Optional[str] = None):
        self.config = ShyamRAGConfig(config_path)
        self.document_processor = AdvancedDocumentProcessor(self.config)
        self.vector_store = HybridVectorStore(self.config)
        self.is_built = False

    def build_database(self, source_directory: Optional[str] = None, force_rebuild: bool = False):
        """Build the RAG database from documents"""
        if source_directory is None:
            source_directory = self.config.get('documents.source_directory', 'Papa/')

        persist_dir = self.config.get('database.persist_directory', 'shyam_rag_db')

        # Check if we can load existing database
        if not force_rebuild and os.path.exists(persist_dir):
            logger.info("Loading existing database...")
            self.vector_store.load_index(persist_dir)
            self.is_built = True
            return

        logger.info(f"Building RAG database from {source_directory}")

        # Get all supported files
        supported_formats = self.config.get('documents.supported_formats', ['.pdf', '.docx'])
        all_files = []

        for format_ext in supported_formats:
            pattern = f"**/*{format_ext}"
            files = list(Path(source_directory).glob(pattern))
            all_files.extend(files)

        logger.info(f"Found {len(all_files)} documents to process")

        # Process documents in batches
        all_chunks = []
        batch_size = 10  # Process 10 files at a time

        for i in tqdm(range(0, len(all_files), batch_size), desc="Processing documents"):
            batch_files = all_files[i:i + batch_size]

            for file_path in batch_files:
                try:
                    chunks = self.document_processor.process_document(str(file_path))
                    all_chunks.extend(chunks)

                except Exception as e:
                    logger.error(f"Error processing {file_path}: {e}")
                    continue

        logger.info(f"Generated {len(all_chunks)} chunks from {len(all_files)} documents")

        # Add to vector store
        if all_chunks:
            self.vector_store.add_documents(all_chunks)

            # Save the database
            self.vector_store.save_index(persist_dir)

            self.is_built = True
            logger.info("RAG database built successfully!")
        else:
            logger.warning("No chunks generated. Database not built.")

    def query(self, question: str, document_type: Optional[str] = None,
              client_name: Optional[str] = None, top_k: int = 5) -> Dict[str, Any]:
        """Query the RAG system"""
        if not self.is_built:
            raise ValueError("Database not built. Call build_database() first.")

        # Prepare metadata filter
        filter_metadata = {}
        if document_type:
            filter_metadata['document_type'] = document_type
        if client_name:
            filter_metadata['client_name'] = client_name

        # Search for relevant documents
        results = self.vector_store.similarity_search(
            question,
            k=top_k,
            filter_metadata=filter_metadata if filter_metadata else None
        )

        # Format response
        response = {
            'query': question,
            'results': [],
            'metadata': {
                'total_results': len(results),
                'filters_applied': filter_metadata,
                'timestamp': datetime.now().isoformat()
            }
        }

        for doc, score in results:
            result_item = {
                'content': doc.page_content,
                'score': float(score),
                'metadata': doc.metadata,
                'source': doc.metadata.get('file_name', 'Unknown')
            }
            response['results'].append(result_item)

        return response

    def get_document_stats(self) -> Dict[str, Any]:
        """Get statistics about the document database"""
        if not self.is_built:
            return {"error": "Database not built"}

        stats = {
            'total_documents': len(self.vector_store.documents),
            'document_types': {},
            'clients': set(),
            'file_types': {},
            'processing_dates': []
        }

        for doc in self.vector_store.documents:
            metadata = doc.metadata

            # Document type distribution
            doc_type = metadata.get('document_type', 'unknown')
            stats['document_types'][doc_type] = stats['document_types'].get(doc_type, 0) + 1

            # Client names
            client = metadata.get('client_name')
            if client:
                stats['clients'].add(client)

            # File types
            file_name = metadata.get('file_name', '')
            ext = Path(file_name).suffix.lower()
            stats['file_types'][ext] = stats['file_types'].get(ext, 0) + 1

            # Processing dates
            proc_date = metadata.get('processing_date')
            if proc_date:
                stats['processing_dates'].append(proc_date)

        stats['clients'] = list(stats['clients'])
        stats['unique_clients'] = len(stats['clients'])

        return stats

    def search_by_client(self, client_name: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """Search documents by client name"""
        return self.query("", client_name=client_name, top_k=top_k)

    def search_by_document_type(self, doc_type: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """Search documents by type (invoice, quotation, account_statement)"""
        return self.query("", document_type=doc_type, top_k=top_k)

    def export_for_n8n(self, query_result: Dict[str, Any]) -> Dict[str, Any]:
        """Export query results in n8n-friendly format"""
        n8n_format = {
            'success': True,
            'query': query_result['query'],
            'total_results': query_result['metadata']['total_results'],
            'documents': []
        }

        for result in query_result['results']:
            doc_info = {
                'content': result['content'],
                'relevance_score': result['score'],
                'source_file': result['source'],
                'document_type': result['metadata'].get('document_type'),
                'client_name': result['metadata'].get('client_name'),
                'file_path': result['metadata'].get('file_path')
            }
            n8n_format['documents'].append(doc_info)

        return n8n_format


# Utility functions for integration
def create_rag_system(config_path: Optional[str] = None) -> ShyamRAGSystem:
    """Factory function to create RAG system"""
    return ShyamRAGSystem(config_path)

def quick_setup(source_directory: str = "Papa/", force_rebuild: bool = False) -> ShyamRAGSystem:
    """Quick setup function for immediate use"""
    logger.info("Setting up Shyam Trading RAG system...")

    rag_system = ShyamRAGSystem()
    rag_system.build_database(source_directory, force_rebuild)

    return rag_system

# Example usage and testing
if __name__ == "__main__":
    # Example configuration
    config_example = {
        "documents": {
            "source_directory": "Papa/",
            "supported_formats": [".pdf", ".docx"],
            "max_file_size_mb": 50
        },
        "chunking": {
            "strategy": "hybrid",
            "chunk_size": 800,
            "chunk_overlap": 200
        },
        "embeddings": {
            "model_name": "sentence-transformers/all-mpnet-base-v2"
        },
        "search": {
            "hybrid_alpha": 0.7,
            "top_k_initial": 20,
            "top_k_final": 5,
            "enable_reranking": True
        }
    }

    # Save example config
    with open("shyam_rag_config.yaml", "w") as f:
        yaml.dump(config_example, f)

    print("🏗️  Shyam Trading Company - Advanced RAG System")
    print("=" * 50)

    try:
        # Initialize system
        rag = quick_setup("Papa/", force_rebuild=False)

        # Get database statistics
        stats = rag.get_document_stats()
        print(f"\n📊 Database Statistics:")
        print(f"   Total documents: {stats['total_documents']}")
        print(f"   Document types: {stats['document_types']}")
        print(f"   Unique clients: {stats['unique_clients']}")
        print(f"   File types: {stats['file_types']}")

        # Example queries
        print(f"\n🔍 Example Queries:")

        # Query 1: General search
        result1 = rag.query("aluminium doors and windows quotation")
        print(f"\n1. Aluminium doors query: {len(result1['results'])} results")

        # Query 2: Client-specific search
        result2 = rag.query("invoice amount", document_type="invoice")
        print(f"2. Invoice search: {len(result2['results'])} results")

        # Query 3: Export for n8n
        n8n_result = rag.export_for_n8n(result1)
        print(f"3. n8n format ready: {n8n_result['success']}")

        print(f"\n✅ RAG system setup complete!")
        print(f"   Use rag.query('your question') to search documents")
        print(f"   Use rag.export_for_n8n(result) for n8n integration")

    except Exception as e:
        logger.error(f"Setup failed: {e}")
        print(f"❌ Setup failed: {e}")
        print(f"   Make sure you have the required packages installed:")
        print(f"   pip install sentence-transformers faiss-cpu PyMuPDF python-docx mammoth spacy rank-bm25 chromadb")
        print(f"   python -m spacy download en_core_web_sm")
