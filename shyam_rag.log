2025-07-18 01:21:27,535 - shyam_rag_system - INFO - Loading embedding model: sentence-transformers/all-mpnet-base-v2
2025-07-18 01:21:27,537 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda:0
2025-07-18 01:21:27,538 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-mpnet-base-v2
2025-07-18 01:21:33,581 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-07-18 01:22:07,282 - shyam_rag_system - INFO - Loading reranker model: cross-encoder/ms-marco-MiniLM-L-6-v2
2025-07-18 01:22:08,596 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-07-18 01:22:18,991 - sentence_transformers.cross_encoder.CrossEncoder - INFO - Use pytorch device: cuda:0
2025-07-18 01:28:48,818 - shyam_rag_system - INFO - Loading embedding model: sentence-transformers/all-mpnet-base-v2
2025-07-18 01:28:48,823 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda:0
2025-07-18 01:28:48,823 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-mpnet-base-v2
2025-07-18 01:28:54,714 - shyam_rag_system - INFO - Loading reranker model: cross-encoder/ms-marco-MiniLM-L-6-v2
2025-07-18 01:28:57,133 - sentence_transformers.cross_encoder.CrossEncoder - INFO - Use pytorch device: cuda:0
2025-07-18 01:28:57,729 - shyam_rag_system - INFO - Loading existing database...
2025-07-18 01:28:57,922 - shyam_rag_system - INFO - Vector store loaded from shyam_rag_db
