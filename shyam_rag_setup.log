2025-07-16 03:48:27,638 - __main__ - INFO - Detected platform: Windows 11
2025-07-16 03:48:27,647 - __main__ - INFO - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-16 03:48:27,648 - __main__ - INFO - Python executable: C:\Users\<USER>\Desktop\shyam-trading\shyam_rag_env\Scripts\python.exe
2025-07-16 03:48:27,648 - __main__ - INFO - Installing required packages...
2025-07-16 03:48:27,648 - __main__ - INFO - Using existing Python environment
2025-07-16 03:48:27,648 - __main__ - INFO - Upgrading pip...
2025-07-16 03:48:27,649 - __main__ - INFO - Running command (attempt 1/3): C:\Users\<USER>\Desktop\shyam-trading\shyam_rag_env\Scripts\python.exe -m pip install --upgrade pip
2025-07-16 03:48:29,822 - __main__ - INFO - Command completed successfully
2025-07-16 03:48:29,823 - __main__ - DEBUG - STDOUT: Requirement already satisfied: pip in c:\users\<USER>\desktop\shyam-trading\shyam_rag_env\lib\site-packages (25.1.1)

2025-07-16 03:48:29,823 - __main__ - INFO - Installing package group 1/9: ['numpy>=1.24.0', 'pandas>=2.0.0']
2025-07-16 03:48:29,823 - __main__ - INFO - Running command (attempt 1/3): C:\Users\<USER>\Desktop\shyam-trading\shyam_rag_env\Scripts\python.exe -m pip install numpy>=1.24.0 pandas>=2.0.0
2025-07-16 03:49:40,763 - __main__ - INFO - Command completed successfully
2025-07-16 03:49:40,763 - __main__ - DEBUG - STDOUT: Collecting numpy>=1.24.0
  Downloading numpy-2.3.1-cp313-cp313-win_amd64.whl.metadata (60 kB)
Collecting pandas>=2.0.0
  Downloading pandas-2.3.1-cp313-cp313-win_amd64.whl.metadata (19 kB)
Collecting python-dateutil>=2.8.2 (from pandas>=2.0.0)
  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting pytz>=2020.1 (from pandas>=2.0.0)
  Using cached pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas>=2.0.0)
  Using cached tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting six>=1.5 (from python-dateutil>=2.8.2->pandas>=2.0.0)
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Downloading numpy-2.3.1-cp313-cp313-win_amd64.whl (12.7 MB)
   ---------------------------------------- 12.7/12.7 MB 14.7 MB/s eta 0:00:00
Downloading pandas-2.3.1-cp313-cp313-win_amd64.whl (11.0 MB)
   ---------------------------------------- 11.0/11.0 MB 1.8 MB/s eta 0:00:00
Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
Using cached pytz-2025.2-py2.py3-none-any.whl (509 kB)
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Using cached tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Installing collected packages: pytz, tzdata, six, numpy, python-dateutil, pandas

Successfully installed numpy-2.3.1 pandas-2.3.1 python-dateutil-2.9.0.post0 pytz-2025.2 six-1.17.0 tzdata-2025.2

2025-07-16 03:49:40,830 - __main__ - INFO - Installing package group 2/9: ['torch>=2.0.0', '--index-url', 'https://download.pytorch.org/whl/cpu']
2025-07-16 03:49:40,831 - __main__ - INFO - Running command (attempt 1/3): C:\Users\<USER>\Desktop\shyam-trading\shyam_rag_env\Scripts\python.exe -m pip install torch>=2.0.0 --index-url https://download.pytorch.org/whl/cpu
2025-07-16 03:51:33,923 - __main__ - INFO - Command completed successfully
2025-07-16 03:51:33,923 - __main__ - DEBUG - STDOUT: Looking in indexes: https://download.pytorch.org/whl/cpu
Collecting torch>=2.0.0
  Using cached https://download.pytorch.org/whl/cpu/torch-2.7.1%2Bcpu-cp313-cp313-win_amd64.whl.metadata (27 kB)
Collecting filelock (from torch>=2.0.0)
  Using cached https://download.pytorch.org/whl/filelock-3.13.1-py3-none-any.whl.metadata (2.8 kB)
Requirement already satisfied: typing-extensions>=4.10.0 in c:\users\<USER>\desktop\shyam-trading\shyam_rag_env\lib\site-packages (from torch>=2.0.0) (4.12.2)
Requirement already satisfied: sympy>=1.13.3 in c:\users\<USER>\desktop\shyam-trading\shyam_rag_env\lib\site-packages (from torch>=2.0.0) (1.13.3)
Collecting networkx (from torch>=2.0.0)
  Using cached https://download.pytorch.org/whl/networkx-3.3-py3-none-any.whl.metadata (5.1 kB)
Collecting jinja2 (from torch>=2.0.0)
  Using cached https://download.pytorch.org/whl/Jinja2-3.1.4-py3-none-any.whl.metadata (2.6 kB)
Collecting fsspec (from torch>=2.0.0)
  Using cached https://download.pytorch.org/whl/fsspec-2024.6.1-py3-none-any.whl.metadata (11 kB)
Collecting setuptools (from torch>=2.0.0)
  Using cached https://download.pytorch.org/whl/setuptools-70.2.0-py3-none-any.whl.metadata (5.8 kB)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\desktop\shyam-trading\shyam_rag_env\lib\site-packages (from sympy>=1.13.3->torch>=2.0.0) (1.3.0)
Collecting MarkupSafe>=2.0 (from jinja2->torch>=2.0.0)
  Using cached MarkupSafe-2.1.5-cp313-cp313-win_amd64.whl
Using cached https://download.pytorch.org/whl/cpu/torch-2.7.1%2Bcpu-cp313-cp313-win_amd64.whl (216.0 MB)
Using cached https://download.pytorch.org/whl/filelock-3.13.1-py3-none-any.whl (11 kB)
Using cached https://download.pytorch.org/whl/fsspec-2024.6.1-py3-none-any.whl (177 kB)
Using cached https://download.pytorch.org/whl/Jinja2-3.1.4-py3-none-any.whl (133 kB)
Using cached https://download.pytorch.org/whl/networkx-3.3-py3-none-any.whl (1.7 MB)
Using cached https://download.pytorch.org/whl/setuptools-70.2.0-py3-none-any.whl (930 kB)
Installing collected packages: setuptools, networkx, MarkupSafe, fsspec, filelock, jinja2, torch

Successfully installed MarkupSafe-2.1.5 filelock-3.13.1 fsspec-2024.6.1 jinja2-3.1.4 networkx-3.3 setuptools-70.2.0 torch-2.7.1+cpu

2025-07-16 03:51:33,928 - __main__ - INFO - Installing package group 3/9: ['transformers>=4.30.0', 'sentence-transformers>=2.2.2', 'datasets>=2.14.0']
2025-07-16 03:51:33,928 - __main__ - INFO - Running command (attempt 1/3): C:\Users\<USER>\Desktop\shyam-trading\shyam_rag_env\Scripts\python.exe -m pip install transformers>=4.30.0 sentence-transformers>=2.2.2 datasets>=2.14.0
